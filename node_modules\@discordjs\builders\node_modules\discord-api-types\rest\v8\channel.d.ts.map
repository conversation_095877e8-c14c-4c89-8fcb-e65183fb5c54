{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EACX,qBAAqB,EACrB,kBAAkB,EAClB,aAAa,EACb,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,4BAA4B,EAC5B,mBAAmB,EACnB,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,oDAAoD,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEjH;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,mCAAmC;IACpF,EAAE,EAAE,SAAS,CAAC;CACd;AAED;;;GAGG;AACH,oBAAY,uBAAuB,GAAG,UAAU,CAAC;AAEjD;;;GAGG;AACH,oBAAY,2BAA2B,GAAG,oDAAoD,CAAC;IAC9F;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;;;OAKG;IACH,IAAI,CAAC,EAAE,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IACrD;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACtB;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,wBAAwB,EAAE,GAAG,IAAI,CAAC;IAC1D;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;IAC7B;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC;CAC7C,CAAC,CAAC;AAEH;;;GAGG;AACH,oBAAY,yBAAyB,GAAG,UAAU,CAAC;AAEnD;;;GAGG;AACH,oBAAY,0BAA0B,GAAG,UAAU,CAAC;AAEpD;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAC9C;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;;GAGG;AACH,oBAAY,+BAA+B,GAAG,UAAU,EAAE,CAAC;AAE3D;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,UAAU,CAAC;AAExD;;;GAGG;AACH,oBAAY,uBAAuB,GAAG,aAAa,CAAC,mBAAmB,CAAC,GACvE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC,GACjD,oDAAoD,CAAC;IACpD;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC7B,CAAC,CAAC;AAEJ;;;GAGG;AACH,oBAAY,iCAAiC,GAAG,oDAAoD,CAAC;IACpG;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IACd;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC;IACpB;;;;;OAKG;IACH,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,CAAC;IACtC;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,uBAAuB,CAAC;IAC5C;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,CAAC;IACnE;;;;OAIG;IACH,WAAW,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACvF;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,GAAG,aAAa,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACvG;;OAEG;IACH,KAAK,CAAC,EAAE,YAAY,CAAC;CACrB,CAAC,CAAC;AAEH;;;GAGG;AACH,oBAAY,qCAAqC,GAC9C,CAAC;IACD;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACrB,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,GACxC,CAAC,iCAAiC,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAE7E;;;GAGG;AACH,oBAAY,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;;GAGG;AACH,oBAAY,wCAAwC,GAAG,UAAU,CAAC;AAElE;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;;GAGG;AACH,oBAAY,6CAA6C,GAAG,KAAK,CAAC;AAMlE,MAAM,WAAW,0CAA0C;IAC1D;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;;GAGG;AACH,oBAAY,2CAA2C,GAAG,OAAO,EAAE,CAAC;AAEpE;;;GAGG;AACH,oBAAY,6CAA6C,GAAG,KAAK,CAAC;AAElE;;;GAGG;AACH,oBAAY,yCAAyC,GAAG,KAAK,CAAC;AAE9D;;;GAGG;AACH,oBAAY,kCAAkC,GAAG,oDAAoD,CAAC;IACrG;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAC3B;;;;;OAKG;IACH,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC;IACxB;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC;IAC5B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC;IAC7C;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;IACvG;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,GAAG,IAAI,CAAC;CAC1E,CAAC,CAAC;AAEH;;;GAGG;AACH,oBAAY,sCAAsC,GAC/C,CAAC;IACD;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACrB,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,GACxC,CAAC,kCAAkC,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAE9E;;;GAGG;AACH,oBAAY,gCAAgC,GAAG,UAAU,CAAC;AAE1D;;;GAGG;AACH,oBAAY,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;GAGG;AACH,MAAM,WAAW,4CAA4C;IAC5D;;OAEG;IACH,QAAQ,EAAE,SAAS,EAAE,CAAC;CACtB;AAED;;;GAGG;AACH,oBAAY,0CAA0C,GAAG,KAAK,CAAC;AAE/D;;;GAGG;AACH,MAAM,WAAW,mCAAmC;IACnD;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;IAC3B;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;IAC1B;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;CACpB;AAED;;;GAGG;AACH,oBAAY,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,iBAAiB,EAAE,CAAC;AAEjE;;;GAGG;AACH,oBAAY,gCAAgC,GAAG,oDAAoD,CAAC;IACnG;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;;;OAIG;IACH,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAC/B;;;;OAIG;IACH,cAAc,CAAC,EAAE,SAAS,CAAC;IAC3B;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,SAAS,CAAC;CAClC,CAAC,CAAC;AAEH;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,iBAAiB,CAAC;AAE/D;;;GAGG;AACH,oBAAY,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;;GAGG;AACH,MAAM,WAAW,mCAAmC;IACnD;;OAEG;IACH,kBAAkB,EAAE,SAAS,CAAC;CAC9B;AAED;;;GAGG;AACH,oBAAY,iCAAiC,GAAG,kBAAkB,CAAC;AAEnE;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;GAGG;AACH,oBAAY,2BAA2B,GAAG,UAAU,EAAE,CAAC;AAEvD;;;GAGG;AACH,oBAAY,0BAA0B,GAAG,KAAK,CAAC;AAE/C;;;GAGG;AACH,oBAAY,6BAA6B,GAAG,KAAK,CAAC;AAElD;;;GAGG;AACH,oBAAY,kCAAkC,GAAG,oDAAoD,CAAC;IACrG;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACd,CAAC,CAAC;AAEH;;;GAGG;AACH,oBAAY,gCAAgC,GAAG,OAAO,CAAC;AAEvD;;;GAGG;AACH,oBAAY,mCAAmC,GAAG,OAAO,CAAC"}