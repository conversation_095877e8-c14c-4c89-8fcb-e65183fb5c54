import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
import type { Snowflake } from '../../../../../globals';
export declare type APIApplicationCommandRoleOption = APIApplicationCommandOptionBase<ApplicationCommandOptionType.Role>;
export declare type APIApplicationCommandInteractionDataRoleOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.Role, Snowflake>;
//# sourceMappingURL=role.d.ts.map