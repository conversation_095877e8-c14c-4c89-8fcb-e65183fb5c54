const { owners, prefix, emco } = require(`${process.cwd()}/config`);
const { MessageActionRow, MessageSelectMenu, MessageButton, MessageEmbed, MessageAttachment } = require('discord.js');

module.exports = {
    name: 'help',
    run: async (client, message) => {
        if (!owners.includes(message.author.id)) return;

        // رابط صورة الخط العلوية للاستخدام في الإيمبدات
        const headerImageUrl = 'https://media.discordapp.net/attachments/1362373235458445605/1377384411313733763/Oryn_5.png';
        const headerImage = new MessageAttachment(headerImageUrl, 'oryn_header.png');

        // إنشاء قائمة الاختيار (Select Menu) بتصميم محسن
        const selectMenu = new MessageSelectMenu()
            .setCustomId('help_menu')
            .setPlaceholder('⌁ اختر قسمًا لعرض الأوامر')
            .addOptions([
                {
                    label: 'أوامر مالك البوت',
                    description: 'عرض الأوامر الخاصة بالعملاء',
                    value: 'vip_commands',
                    emoji: '<:1327148990961287188:1327148990961287188>'
                },
                {
                    label: 'أوامر التوكنات',
                    description: 'الأوامر المتعلقة بإدارة التوكنات',
                    value: 'token_commands',
                    emoji: '<:1327148990961287188:1327148990961287188>'
                },
                {
                    label: 'أوامر الاشتراكات',
                    description: 'الأوامر المتعلقة بإدارة الاشتراكات',
                    value: 'subscription_commands',
                    emoji: '<:1327148990961287188:1327148990961287188>'
                },
                {
                    label: 'أوامر الدعم الفني',
                    description: 'أوامر خاصة بالدعم الفني وإدارة النظام',
                    value: 'support_commands',
                    emoji: '<:1327148990961287188:1327148990961287188>'
                }
            ]);

        // إنشاء أزرار تفاعلية لتحسين تجربة المستخدم
        const websiteButton = new MessageButton()
            .setLabel('متجر اورين')
            .setURL('https://discord.gg/D2GuZNGwuh')
            .setStyle('LINK')
            .setEmoji('<:1327148990961287188:1327148990961287188>');

        const supportButton = new MessageButton()
            .setLabel('الدعم الفني')
            .setURL('https://discord.gg/D2GuZNGwuh')
            .setStyle('LINK')
            .setEmoji('<:1327148990961287188:1327148990961287188>');

        // تجميع المكونات
        const row1 = new MessageActionRow().addComponents(selectMenu);
        const row2 = new MessageActionRow().addComponents(websiteButton, supportButton);

        // إنشاء إيمبد جميل للرسالة مع تصميم محسن
        const embed = new MessageEmbed()
            .setColor(emco)
            .setTitle('┊قائمة الأوامر | Oryn Store')
            .setDescription('```md\n# مرحبًا بك في نظام إدارة خدمات اورين\n```\n**♢ استخدم القائمة المنسدلة أدناه للتنقل بين الأقسام**\n**♢ اختر القسم المناسب لعرض الأوامر التفصيلية الخاصة به**')
            .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
            .setImage(headerImageUrl)
            .setFooter({ text: `طلبه بواسطة: ${message.author.username}`, iconURL: message.author.displayAvatarURL({ dynamic: true }) })
            .setTimestamp();

        await message.reply({
            embeds: [embed],
            components: [row1, row2],
            files: [headerImage]
        });

        // فلتر للتفاعلات (لضمان أن المستخدم الصحيح هو من يستجيب)
        const filter = interaction => interaction.customId === 'help_menu' && interaction.user.id === message.author.id;
        
        const collector = message.channel.createMessageComponentCollector({ 
            filter, 
            time: 120000 // تمديد وقت انتهاء التفاعل
        });

        collector.on('collect', async interaction => {
            if (interaction.values[0] === 'vip_commands') {
                const vipEmbed = new MessageEmbed()
                    .setColor(emco)
                    .setTitle('┊أوامر مالك البوت')
                    .setDescription('```md\n# أوامر إدارة البوتات والعملاء\n```')
                    .addFields(
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر الإدارة الأساسية', value: 
                        '`vip` : عرض لوحة الإدارة الخاصة بالعملاء\n`addsub` : إضافة بوتات ميوزك للعميل\n`removebots` : حذف بوتات من العميل' },
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر العرض والمراقبة', value: 
                        '`bots` : عرض قائمة سيرفرات البوتات للعملاء\n`stats` : عرض إحصائيات استخدام النظام' }
                    )
                    .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
                    .setImage(headerImageUrl)
                    .setFooter({ text: `طلبه بواسطة: ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
                    .setTimestamp();
                
                // إضافة زر العودة للصفحة الرئيسية
                const backButton = new MessageButton()
                    .setCustomId('back_to_main')
                    .setLabel('العودة للقائمة الرئيسية')
                    .setStyle('SECONDARY')
                    .setEmoji('<:1327148990961287188:1327148990961287188>');
                
                const navigationRow = new MessageActionRow().addComponents(backButton);
                
                await interaction.update({ 
                    embeds: [vipEmbed], 
                    components: [row1, navigationRow] 
                });
            }
            else if (interaction.values[0] === 'token_commands') {
                const tokenEmbed = new MessageEmbed()
                    .setColor(emco)
                    .setTitle('┊أوامر إدارة التوكنات')
                    .setDescription('```md\n# أوامر إدارة وإضافة التوكنات\n```')
                    .addFields(
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر إضافة التوكنات', value: 
                        '`addtoken` : إضافة توكن جديد للمخزن\n`bulktoken` : إضافة مجموعة توكنات دفعة واحدة' },
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر عرض التوكنات', value: 
                        '`tokens` : عرض قائمة التوكنات المخزنة بالنظام\n`tokeninfo` : عرض معلومات توكن محدد' }
                    )
                    .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
                    .setImage(headerImageUrl)
                    .setFooter({ text: `طلبه بواسطة: ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
                    .setTimestamp();
                
                // إضافة زر العودة للصفحة الرئيسية
                const backButton = new MessageButton()
                    .setCustomId('back_to_main')
                    .setLabel('العودة للقائمة الرئيسية')
                    .setStyle('SECONDARY')
                    .setEmoji('<:1327148990961287188:1327148990961287188>');
                
                const navigationRow = new MessageActionRow().addComponents(backButton);
                
                await interaction.update({ 
                    embeds: [tokenEmbed], 
                    components: [row1, navigationRow] 
                });
            }
            else if (interaction.values[0] === 'subscription_commands') {
                const subEmbed = new MessageEmbed()
                    .setColor(emco)
                    .setTitle('┊أوامر إدارة الاشتراكات')
                    .setDescription('```md\n# أوامر إدارة اشتراكات العملاء\n```')
                    .addFields(
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر إدارة الوقت', value: 
                        '`addsubtime` : إضافة وقت إضافي لاشتراك عميل\n`extend` : تمديد اشتراك لفترة محددة' },
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر العرض والإدارة', value: 
                        '`allsub` : عرض قائمة جميع الاشتراكات النشطة\n`mysub` : عرض تفاصيل اشتراكاتك الحالية\n`removesub` : إلغاء اشتراك محدد' }
                    )
                    .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
                    .setImage(headerImageUrl)
                    .setFooter({ text: `طلبه بواسطة: ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
                    .setTimestamp();
                
                // إضافة زر العودة للصفحة الرئيسية
                const backButton = new MessageButton()
                    .setCustomId('back_to_main')
                    .setLabel('العودة للقائمة الرئيسية')
                    .setStyle('SECONDARY')
                    .setEmoji('<:1327148990961287188:1327148990961287188>');
                
                const navigationRow = new MessageActionRow().addComponents(backButton);
                
                await interaction.update({ 
                    embeds: [subEmbed], 
                    components: [row1, navigationRow] 
                });
            }
            else if (interaction.values[0] === 'support_commands') {
                const supportEmbed = new MessageEmbed()
                    .setColor(emco)
                    .setTitle('┊أوامر الدعم الفني')
                    .setDescription('```md\n# أوامر دعم النظام وإدارة الخوادم\n```')
                    .addFields(
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر الصيانة', value: 
                        '`restart` : إعادة تشغيل نظام البوتات\n`maintenance` : وضع النظام في وضع الصيانة' },
                        { name: '<:1327148990961287188:1327148990961287188> | أوامر الدعم', value: 
                        '`support` : فتح تذكرة دعم فني\n`status` : عرض حالة الخدمات والخوادم\n`report` : الإبلاغ عن مشكلة في النظام' }
                    )
                    .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
                    .setImage(headerImageUrl)
                    .setFooter({ text: `طلبه بواسطة: ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
                    .setTimestamp();
                
                // إضافة زر العودة للصفحة الرئيسية
                const backButton = new MessageButton()
                    .setCustomId('back_to_main')
                    .setLabel('العودة للقائمة الرئيسية')
                    .setStyle('SECONDARY')
                    .setEmoji('<:1327148990961287188:1327148990961287188>');
                
                const navigationRow = new MessageActionRow().addComponents(backButton);
                
                await interaction.update({ 
                    embeds: [supportEmbed], 
                    components: [row1, navigationRow] 
                });
            }
        });

        // معالج أحداث النقر على الأزرار
        const buttonCollector = message.channel.createMessageComponentCollector({
            filter: (i) => i.customId === 'back_to_main' && i.user.id === message.author.id,
            time: 120000
        });

        buttonCollector.on('collect', async (interaction) => {
            if (interaction.customId === 'back_to_main') {
                // إعادة إنشاء الإيمبد الرئيسي
                const mainEmbed = new MessageEmbed()
                    .setColor(emco)
                    .setTitle('┊قائمة الأوامر | Oryn Store')
                    .setDescription('```md\n# مرحبًا بك في نظام إدارة خدمات اورين\n```\n**♢ استخدم القائمة المنسدلة أدناه للتنقل بين الأقسام**\n**♢ اختر القسم المناسب لعرض الأوامر التفصيلية الخاصة به**')
                    .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
                    .setImage(headerImageUrl)
                    .setFooter({ text: `طلبه بواسطة: ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
                    .setTimestamp();
                
                // عند النقر على زر العودة، نقوم بعرض الصفحة الرئيسية مرة أخرى
                await interaction.update({
                    embeds: [mainEmbed],
                    components: [row1, row2]
                });
            }
        });

        collector.on('end', collected => {
            if (collected.size === 0) {
                const timeoutEmbed = new MessageEmbed()
                    .setColor(emco)
                    .setTitle('انتهاء وقت القائمة')
                    .setDescription('```md\n# انتهت مهلة التفاعل\n```\n<:1327148990961287188:1327148990961287188> انتهى وقت الاستجابة، يرجى استخدام أمر `!help` مجدداً للحصول على المساعدة.')
                    .setFooter({ text: `Oryn Store`, iconURL: client.user.displayAvatarURL() });
                
                try {
                    // استخدام طريقة بديلة للتعديل التي تعمل على الرسائل القديمة
                    const lastMessage = message.channel.messages.cache.get(message.id);
                    if (lastMessage) {
                        lastMessage.edit({ embeds: [timeoutEmbed], components: [] });
                    }
                } catch (error) {
                    console.error("فشل في تحديث رسالة منتهية الصلاحية:", error);
                }
            }
        });
    }
}