{"version": 3, "file": "PlayerManager.js", "sourceRoot": "", "sources": ["../../../src/management/PlayerManager.ts"], "names": [], "mappings": ";;;AAgFA,kDA6CC;AA5HD,uCAAkF;AAClF,MAAa,aAAa;IACf,OAAO,CAAU;IACnB,KAAK,GAAwB,IAAI,GAAG,EAAE,CAAC;IAC9C,YAAY,OAAgB;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IACM,MAAM,CAAC,MAAqB;QACjC,IAAA,wBAAgB,EACd,MAAM,CAAC,OAAO,EACd,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,QAAQ,EAClD,8CAA8C,CAC/C,CAAC;QAEF,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAA,wBAAgB,EACd,MAAM,CAAC,cAAc,EACrB,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,QAAQ,EACjD,qDAAqD,CACtD,CAAC;QACF,IAAA,wBAAgB,EACd,MAAM,CAAC,aAAa,EACpB,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,QAAQ,EACjD,oDAAoD,CACrD,CAAC;QACF,IAAA,wBAAgB,EACd,MAAM,CAAC,MAAM,EACb,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,CAAC,EAC1C,mFAAmF,CACpF,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,IAAA,wBAAgB,EACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EACnC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,EAC5B,uCAAuC,CACxC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,CAAC;YAC1F,IAAI,CAAC,IAAI;gBAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAE1E,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC;QAC7C,CAAC;QAED,MAAM,MAAM,GAAW,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3E,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAEvC,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,OAAO,EACP,4CAA4C,GAAG,MAAM,CAAC,OAAO,GAAG,mBAAmB,EACnF,MAAM,CACP,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IACM,GAAG,CAAC,OAAe;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IACM,GAAG,CAAC,OAAe;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IACM,KAAK,CAAC,MAAM,CAAC,OAAe;QACjC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAAE,OAAO;QAC/B,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,OAAO,EACP,4CAA4C,GAAG,OAAO,GAAG,mBAAmB,CAC7E,CAAC;IACJ,CAAC;IACD,IAAW,GAAG;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAClC,CAAC;CACF;AA5ED,sCA4EC;AAEM,KAAK,UAAU,mBAAmB,CAAC,MAAM;IAC9C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IAEjG,MAAM,QAAQ,GAAG,CAAC,OAAe,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,iBAAiB,OAAO,EAAE,CAAC,CAAC;IAE/F,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;QAClC,IACE,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO;YAC3B,MAAM,CAAC,cAAc;YACrB,MAAM,CAAC,OAAO;YACd,CAAC,MAAM,CAAC,SAAS,EACjB,CAAC;YACD,QAAQ,CACN,0CAA0C,MAAM,CAAC,cAAc,cAAc,MAAM,CAAC,OAAO,EAAE,CAC9F,CAAC;YACF,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;YACvB,MAAM,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC;IACpC,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;QAC9B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACpD,QAAQ,CAAC,4CAA4C,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACvE,MAAM,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC;IACpC,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,UAAU,EAAE,SAAS,KAAK,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC;IAEtF,MAAM,WAAW,GAAG,MAAM,gBAAgB,EAAE,CAAC;IAC7C,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,MAAM,YAAY,EAAE,CAAC,EAAE,CAAC;QAC5C,QAAQ,CACN,sCAAsC,MAAM,CAAC,cAAc,cAAc,MAAM,CAAC,OAAO,gFAAgF,CACxK,CAAC;QACF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,eAAe,EAAE,EAAE,CAAC;QACtB,QAAQ,CAAC,oCAAoC,MAAM,CAAC,OAAO,oBAAoB,CAAC,CAAC;QACjF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}