const fs = require('fs');
const { owners, prefix, emco } = require(`${process.cwd()}/config`);
const { MessageEmbed } = require('discord.js');

// دالة للتأكد من سلامة ملف السجلات
function ensureLogsFileExists() {
  try {
    if (!fs.existsSync('./logs.json')) {
      fs.writeFileSync('./logs.json', '[]', 'utf8');
      console.log('تم إنشاء ملف logs.json جديد');
      return true;
    }
    
    const content = fs.readFileSync('./logs.json', 'utf8');
    if (!content || content.trim() === '') {
      fs.writeFileSync('./logs.json', '[]', 'utf8');
      console.log('تم إعادة تهيئة ملف logs.json الفارغ');
      return true;
    }
    
    try {
      JSON.parse(content);
      return true;
    } catch (error) {
      console.error('ملف logs.json غير صالح، تتم إعادة تهيئته:', error);
      fs.writeFileSync('./logs.json', '[]', 'utf8');
      return true;
    }
  } catch (error) {
    console.error('حدث خطأ أثناء التحقق من ملف logs.json:', error);
    return false;
  }
}

module.exports = {
  name: 'allsub',
  aliases: ["allsub"],
  run: async (client, message, args) => {
    if (!owners.includes(message.author.id)) return;    try {
      // التحقق من وجود الملف وصحته
      if (!ensureLogsFileExists()) {
        return message.reply('**حدث خطأ أثناء التحقق من ملف السجلات. الرجاء المحاولة لاحقًا.**');
      }
      
      const logs = fs.readFileSync('./logs.json', 'utf8');
      // تحليل محتوى الملف بشكل آمن
      let logsArray = [];
      try {
        logsArray = JSON.parse(logs);
        if (!Array.isArray(logsArray)) {
          throw new Error('تنسيق البيانات غير صحيح');
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
        fs.writeFileSync('./logs.json', '[]', 'utf8');
        message.reply('**تم إصلاح ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        return;
      }

      if (logsArray.length === 0) {
        return message.reply('**لا توجد اشتراكات مسجلة حاليًا.**');
      }

      // ترتيب الاشتراكات بناءً على اسم المستخدم
      logsArray.sort((a, b) => a.user.localeCompare(b.user));

      const embed = new MessageEmbed()
        .setTitle('All Subscriptions')
        .setColor(emco)
        .setThumbnail("https://media.discordapp.net/attachments/1362373235458445605/1377384410839650366/Oryn_9.png?ex=683d61f2&is=683c1072&hm=c4cbe47f27039b844f24d50c5c9923adca04d9ca195807f60761c4138b901315&=&format=webp&quality=lossless&width=864&height=486")
        .setFooter(`${message.client.user.username} | Timer`, `${message.client.user.displayAvatarURL({ dynamic: true })}`);

      logsArray.forEach((userSubscription, index) => {
        const expirationTime = userSubscription.expirationTime;
        const remainingTime = expirationTime - Date.now();

        // حساب الأيام والساعات والدقائق والثواني المتبقية
        const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
        const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

        const formattedTime = `${days ? `${days}d ` : ''}${hours ? `${hours}h ` : ''}${minutes ? `${minutes}m ` : ''}${seconds ? `${seconds}s` : ''}`;
        
        const guildId = userSubscription.server; // قم بتحديد الخاصية المناسبة من كائن الاشتراك

        embed.setDescription(`${embed.description || ''}\n**\`${index + 1}\` | <@${userSubscription.user}> | \`Music x${userSubscription.botsCount}\` | \`${userSubscription.code}\` | ${formattedTime} **`);
      });

      // إرسال رسالة الرد ك Embed
      message.reply({ embeds: [embed] });    } catch (error) {
      console.error('❌> خطأ في أمر allsub:', error);
      
      // تحديد نوع الخطأ والتعامل معه بشكل مناسب
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        console.error('❌> خطأ في تحليل بيانات JSON:', error);
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          message.reply('**تم إصلاح ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        } catch (writeError) {
          console.error('❌> فشل في إصلاح ملف السجلات:', writeError);
          message.reply('**حدث خطأ أثناء محاولة إصلاح ملف السجلات. الرجاء الاتصال بمسؤول النظام.**');
        }
      } else if (error.code === 'ENOENT') {
        // خطأ عدم وجود الملف
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          message.reply('**تم إنشاء ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        } catch (writeError) {
          console.error('❌> فشل في إنشاء ملف السجلات:', writeError);
          message.reply('**حدث خطأ أثناء محاولة إنشاء ملف السجلات. الرجاء الاتصال بمسؤول النظام.**');
        }
      } else {
        // أخطاء أخرى غير متوقعة
        message.reply('**حدث خطأ أثناء قراءة بيانات الاشتراكات. الرجاء المحاولة لاحقًا.**');
      }
    }
  }
};
