const fs = require('fs');
const { owners, prefix, emco, useEmbeds, Support} = require(`${process.cwd()}/config`);
const { MessageEmbed, Client } = require('discord.js');

module.exports = {
  name: 'removesub',
  aliases: ["remove"],
  run: async (client, message, args) => {
    if (!owners.includes(message.author.id)) return;

    if (message.author.bot) return;

    const codeToRemove = args[0];
    if (!codeToRemove) return message.reply("**الرجاء تحديد ايدي الاشتراك الذي تريد إزالته.**");

    let removedTokens = [];    try {
      // التحقق من وجود الملف
      if (!fs.existsSync('./logs.json')) {
        fs.writeFileSync('./logs.json', '[]', 'utf8');
        console.log('تم إنشاء ملف logs.json جديد');
        return message.reply("**تم إنشاء ملف السجلات. لا توجد أي اشتراكات مسجلة.**");
      }
      
      const logs = fs.readFileSync('./logs.json', 'utf8');
      // تحليل محتوى الملف بشكل آمن
      let logsArray = [];
      try {
        if (logs.trim() === '') {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          return message.reply("**لا توجد أي اشتراكات مسجلة.**");
        }
        
        logsArray = JSON.parse(logs);
        if (!Array.isArray(logsArray)) {
          throw new Error('تنسيق البيانات غير صحيح');
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
        fs.writeFileSync('./logs.json', '[]', 'utf8');
        return message.reply('**تم إصلاح ملف السجلات. لا توجد أي اشتراكات مسجلة.**');
      }

      const matchingSubscriptions = logsArray.filter(entry => entry.code === codeToRemove);

      if (matchingSubscriptions.length === 0) {
        return message.reply("**لا يوجد اشتراكات مرتبطة بهذا الايدي.**");
      }

      // حذف الاشتراك من ملف logs.json
      matchingSubscriptions.forEach(subscription => {
        logsArray.splice(logsArray.indexOf(subscription), 1);
      });

      // تحديث ملف logs.json بعد الإزالة
      fs.writeFileSync('./logs.json', JSON.stringify(logsArray, null, 2));

      // حذف التوكنات المرتبطة بالكود من ملف tokens.json
      const tokens = fs.readFileSync('./tokens.json', 'utf8');
      let tokensArray = JSON.parse(tokens);
      if (!Array.isArray(tokensArray)) {
        tokensArray = [];
      }

      const tokensToRemove = tokensArray.filter(tokenEntry => matchingSubscriptions.some(subscription => tokenEntry.code === subscription.code));
      tokensArray = tokensArray.filter(tokenEntry => !tokensToRemove.includes(tokenEntry));

      // إضافة التوكنات المحذوفة إلى ملف bots.json
      const bots = fs.readFileSync('./bots.json', 'utf8');
      let botsArray = JSON.parse(bots);
      if (!Array.isArray(botsArray)) {
        botsArray = [];
      }

      tokensToRemove.forEach(tokenEntry => {
        botsArray.push({
          token: tokenEntry.token,
          Server: null,
          channel: null,
          chat: null,
          status: null,
          client: null,
          useEmbeds: false
        });
        removedTokens.push(tokenEntry);
      });

      // تحديث ملف bots.json بعد الإضافة
      fs.writeFileSync('./bots.json', JSON.stringify(botsArray, null, 2));

      // تحديث ملف tokens.json بعد الإزالة
      fs.writeFileSync('./tokens.json', JSON.stringify(tokensArray, null, 2));

      // رد برد التأكيد
      message.react('✅');    } catch (error) {
      console.error('❌> خطأ في أمر removesub:', error);
      
      // تحديد نوع الخطأ والتعامل معه بشكل مناسب
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        console.error('❌> خطأ في تحليل بيانات JSON:', error);
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          message.reply('**تم إصلاح ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        } catch (writeError) {
          console.error('❌> فشل في إصلاح ملف السجلات:', writeError);
          message.reply('**حدث خطأ أثناء محاولة إصلاح ملف السجلات. الرجاء الاتصال بمسؤول النظام.**');
        }
      } else if (error.code === 'ENOENT') {
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          message.reply('**تم إنشاء ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        } catch (writeError) {
          console.error('❌> فشل في إنشاء ملف السجلات:', writeError);
          message.reply('**حدث خطأ أثناء محاولة إنشاء ملف السجلات. الرجاء الاتصال بمسؤول النظام.**');
        }
      } else {
        message.reply('**حدث خطأ أثناء محاولة إزالة الاشتراك. الرجاء المحاولة لاحقًا.**');
      }
    }

    // الجزء الجديد
    const numberOfBotsReset = removedTokens.length;

    // دالة setTimeout لتأخير تغيير التوكنات
    setTimeout(async () => {
      removedTokens.forEach(async (token) => {
        try {
          const randomName = `MrDoCToR-${Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000}`;
          const botClient = new Client({
            intents: [
              'GUILDS',
              'GUILD_MEMBERS',
              'GUILD_MESSAGES',
            ],
          });

          await botClient.login(token.token);

          botClient.guilds.cache.forEach(async (guild) => {
            await guild.leave();
          });

          await botClient.user.setAvatar('https://cdn.discordapp.com/attachments/1202803499260182599/1204801766059941888/bot5.png?ex=65d60e15&is=65c39915&hm=5c042cb015b91167a817fb32740ec1932c1f2e388a2f2922daef15c97f2186bd&');
          await botClient.user.setUsername(randomName);

          await botClient.destroy();
        } catch (error) {
          console.error(`حدث خطأ أثناء تشغيل التوكن: ${error}`);
        }
      });

      // إرسال Embed بعد الانتهاء
      const successEmbed = new MessageEmbed()
        .setTitle("إستخدام ناجح ✅")
        .setColor(emco)
        .setDescription(`**تم إعادة تعيين \`\`${numberOfBotsReset}\`\` بوت وحفظهم في المخزن بنجاح!.**`);
      message.reply({ embeds: [successEmbed] });
    }, 0);  // يمكنك ضبط القيمة إلى الوقت الذي تشاء لتأخير التنفيذ
  }
};
