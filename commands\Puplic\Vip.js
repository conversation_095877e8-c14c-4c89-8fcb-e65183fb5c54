const fs = require('fs');
let fetch;
try {
  fetch = require('node-fetch');
} catch (error) {
  // If node-fetch is not available, use global fetch if available
  if (typeof global.fetch === 'function') {
    fetch = global.fetch;
  } else {
    console.warn('node-fetch is not installed and global fetch is not available. Banner changes may not work.');
    fetch = async () => { throw new Error('Fetch is not available'); };
  }
}
const { Client, Intents, MessageActionRow, MessageSelectMenu, MessageButton, MessageEmbed } = require('discord.js');

module.exports = {
  name: 'vip',
  run: async (client, message) => {
    if (message.author.bot) return;
    const userId = message.author.id;

    if (!fs.existsSync('./tokens.json')) {
      return;
    }

    let tokens = [];
    try {
      const tokensData = fs.readFileSync('./tokens.json', 'utf8');
      tokens = JSON.parse(tokensData);
    } catch (error) {
      console.error('حدث خطأ أثناء قراءة الملف tokens.json:', error);
      return message.reply('حدث خطأ أثناء قراءة الملف.');
    }

    const userTokens = tokens.filter(token => token.client === userId);

    if (userTokens.length === 0) {
      return;
    }

    const selectMenu = new MessageSelectMenu()
      .setCustomId('musicOptions')
      .setPlaceholder('يرجى الاختيار ..')
      .addOptions([
        {
          label: 'روابط البوتات',
          emoji: '🔗',
          description: 'احصل على روابط جميع برامج البوتات التي تمتلكها',
          value: 'allBotsLinks',
        },
        {
          label: 'إدارة السيرفرات',
          emoji: '🛠️',
          description: 'نقل وإضافة السيرفرات حيث توجد البوتات',
          value: 'updateServerId',
        },
        {
          label: 'تغيير صور',
          emoji: '🖼️',
          description: 'تغيير صورة جميع البوتات',
          value: 'changeBotAvatars',
        },
        {
          label: 'تغيير البانر',
          emoji: '🖼️',
          description: 'تغيير بانر جميع البوتات',
          value: 'changeBotBanners',
        },
        {
          label: 'إعادة التشغيل',
          emoji: '🔄',
          description: 'إعادة تشغيل جميع البوتات التي تمتلكها',
          value: 'restartAllBots',
        },
        {
          label: 'تغير الحالة',
          emoji: '📊',
          description: 'تغير حالة جميع البوتات',
          value: 'changeBotStatus',
        },
        {
          label: 'نقل الملكية',
          emoji: '🔄',
          description: 'نقل ملكية البوتات إلى شخص آخر',
          value: 'transferOwnership',
        }
      ]);

    const deleteButton = new MessageButton()
      .setCustomId('Cancel3')
      .setLabel('إلغاء')
      .setEmoji('❌')
      .setStyle('DANGER');

    const totalBots = userTokens.length;

    message.reply({
      content: `**إجمالي عدد البوتات هو: ${totalBots}**`,
      components: [
        new MessageActionRow().addComponents(selectMenu),
        new MessageActionRow().addComponents(deleteButton)
      ],
    });

    const filter = (interaction) => interaction.user.id === message.author.id;
    const collector = message.channel.createMessageComponentCollector({ filter, componentType: 'SELECT_MENU', time: 60000 });

    collector.on('collect', async (interaction) => {
      collector.stop();

      if (interaction.customId === 'Cancel3') {
        await interaction.message.delete();
        return;
      }

      if (!interaction.values || !interaction.values[0]) {
        console.error('No values provided in the interaction.');
        return;
      }

      const selectedOption = interaction.values[0];

      if (selectedOption === 'allBotsLinks') {
        await interaction.deferReply();
        let botInfoPromises = [];
        let totalBots = userTokens.length;

        for (let [index, token] of userTokens.entries()) {
            const botIntents = new Intents([
                Intents.FLAGS.GUILDS,
                Intents.FLAGS.GUILD_MESSAGES,
                Intents.FLAGS.GUILD_MESSAGE_REACTIONS
            ]);
            const bot = new Client({ intents: botIntents });

            botInfoPromises.push(new Promise(async (resolve, reject) => {
                try {
                    await bot.login(token.token);
                    const botInfo = `\`${index + 1}\` - \`${bot.user?.username || "غير معروف"}\` https://discord.com/api/oauth2/authorize?client_id=${bot.user?.id}&permissions=0&scope=bot`;
                    resolve(botInfo);
                } catch (err) {
                    reject(err);
                }
            }));
        }

        Promise.all(botInfoPromises)
            .then(botInfos => {
                botInfos.forEach((botInfo, index) => {
                    interaction.user.send(`**🔗 : رابط بوت الميوزك رقم ${index + 1}:**\n${botInfo}`)
                        .catch(() => {
                            console.error("حدث خطأ أثناء إرسال الرابط:");
                        });
                });
                interaction.followUp({ content: `*تم إرسال روابط جميع البوتات، ${totalBots} بوت.*` });
            })
            .catch(err => {
                console.error("حدث خطأ أثناء تسجيل الدخول:", err);
                interaction.followUp({ content: "حدث خطأ أثناء تسجيل الدخول." });
            });

      } else if (selectedOption === 'updateServerId') {
        await interaction.deferReply();
        const replyMessage = await interaction.followUp({ content: '**يرجى إرفاق ايدي السيرفر المُرد فالشات.**', ephemeral: true });

        const serverIdFilter = (response) => response.author.id === message.author.id && response.content.trim().length > 0;
        const serverIdCollector = message.channel.createMessageCollector({ filter: serverIdFilter, time: 10000 });

        serverIdCollector.on('collect', async (response) => {
          const newServerId = response.content.trim();
          for (const token of userTokens) {
            token.Server = newServerId;
          }
          fs.writeFileSync('./tokens.json', JSON.stringify(tokens, null, 2));
          await message.react("✅");

          let botInfoPromises = [];
          for (let [index, token] of userTokens.entries()) {
              const botIntents = new Intents([
                  Intents.FLAGS.GUILDS,
                  Intents.FLAGS.GUILD_MESSAGES,
                  Intents.FLAGS.GUILD_MESSAGE_REACTIONS
              ]);
              const bot = new Client({ intents: botIntents });

              botInfoPromises.push(new Promise(async (resolve, reject) => {
                  try {
                      await bot.login(token.token);
                      for (const guild of bot.guilds.cache.values()) {
                          if (guild.id === newServerId) continue;
                          if (guild.ownerId === bot.user.id) continue;
                          try {
                              await guild.leave();
                          } catch (error) {
                              console.error(`حدث خطأ أثناء محاولة مغادرة السيرفر ${guild.id}:`, error.message);
                          }
                      }
                      const botInfo = `\`${index + 1}\` - \`${bot.user?.username || "غير معروف"}\` https://discord.com/api/oauth2/authorize?client_id=${bot.user?.id}&permissions=0&scope=bot`;
                      resolve(botInfo);
                  } catch (err) {
                      reject(err);
                  }
              }));
          }

          Promise.all(botInfoPromises)
              .then(botInfos => {
                  message.author.send(`**🔗 : روابط البوتات التي تم نقلها**\n${botInfos.join('\n')}`)
                      .then(() => {
                          const embed = new MessageEmbed()
                              .setTitle("إستخدام ناجح ✅")
                              .setDescription(`تم إرسال رابط جميع البوتات، ${botInfos.length} بوت.`)
                              .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1190035435834048512/312-312-max.png?ex=65a055dd&is=658de0dd&hm=2455805a197d0d4019e20d18ca202314bd3308e241f839095b495e4ef94797f4&");
                          interaction.followUp({ embeds: [embed] });
                      })
                      .catch(() => {
                          const embed = new MessageEmbed()
                              .setTitle("خطأ في الإرسال ❌")
                              .setDescription('حدثت مشكلة أثناء إرسال الروابط في الخاص.');
                          interaction.followUp({ embeds: [embed] });
                      });
              })
              .catch(err => {
                  console.error("حدث خطأ أثناء تسجيل الدخول:", err);
              });

          serverIdCollector.stop();
        });

      } else if (selectedOption === 'changeBotAvatars') {
        await interaction.deferReply();
        const promptEmbed = new MessageEmbed()
            .setDescription(`<@${interaction.user.id}>\nيرجى إرفاق الصورة الجديدة، ملاحظة: يجب أن تكون الصورة مرفقة كـ صورة وليس رابط وأن يكون حجم الصورة أقل من 10 ميغابايت`);

        const cancelButton = new MessageButton()
            .setCustomId('cancelChangeAvatar')
            .setLabel('الغاء')
            .setStyle('DANGER');

        const actionRow = new MessageActionRow().addComponents(cancelButton);
        const replyMessage = await interaction.followUp({ embeds: [promptEmbed], components: [actionRow] });

        const buttonCollector = replyMessage.createMessageComponentCollector({
            filter: i => i.user.id === message.author.id,
            time: 70000
        });

        const messageCollector = interaction.channel.createMessageCollector({
            filter: m => m.author.id === interaction.user.id && m.attachments.size > 0,
            time: 70000
        });

        buttonCollector.on('collect', async (buttonInteraction) => {
            if (buttonInteraction.customId === 'cancelChangeAvatar') {
                await buttonInteraction.update({ content: 'تم الغاء العملية.', components: [] });
                buttonCollector.stop();
                messageCollector.stop();
                return;
            }
        });

        messageCollector.on('collect', async (message) => {
            const imageUrl = message.attachments.first().url;
            for (const tokenData of userTokens) {
                const botIntents = new Intents([
                    Intents.FLAGS.GUILDS,
                    Intents.FLAGS.GUILD_MESSAGES,
                    Intents.FLAGS.GUILD_MESSAGE_REACTIONS
                ]);
                const bot = new Client({ intents: botIntents });

                try {
                    await bot.login(tokenData.token);
                    await bot.user.setAvatar(imageUrl);
                    await bot.destroy();
                } catch (err) {
                    console.error(`حدث خطأ أثناء تغيير صورة البوت: ${bot.user?.username || "غير معروف"} - ${err.message}`);
                }
            }

            const successEmbed = new MessageEmbed()
                .setTitle("إستخدام ناجح ✅")
                .setDescription(`**تم تغير صور جميع البوتات بنجاح** .`);
            await replyMessage.edit({ components: [] });
            interaction.followUp({ embeds: [successEmbed] });
            messageCollector.stop();
            buttonCollector.stop();
        });

      }else if (selectedOption === 'changeBotBanners') {
    await interaction.deferReply();

    const promptEmbed = new MessageEmbed()
        .setColor('#355a5d')
        .setTitle('🖼️ تغيير بانر البوتات')
        .setDescription(`<@${interaction.user.id}>\nيرجى إرفاق صورة البانر الجديدة للبوتات`)
        .addFields(
            { 
                name: '📋 المتطلبات الفنية للصورة', 
                value: '• **الحجم:** أقل من 10 ميغابايت\n• **التنسيق:** PNG أو JPG\n• **الأبعاد المثالية:** 960×540 بكسل' 
            },
            {
                name: '⚠️ ملاحظة',
                value: 'الميزة تعمل فقط مع بوتات التطبيقات الرسمية المعتمدة من ديسكورد'
            }
        )
        .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
        .setFooter({ text: 'Oryn Store | تغيير بانر البوتات', iconURL: client.user.displayAvatarURL() });

    const cancelButton = new MessageButton()
        .setCustomId('cancelChangeBanner')
        .setLabel('إلغاء العملية')
        .setStyle('DANGER')
        .setEmoji('❌');

    const actionRow = new MessageActionRow().addComponents(cancelButton);

    const replyMessage = await interaction.followUp({
        embeds: [promptEmbed],
        components: [actionRow]
    });

    // إضافة معالج لزر الإلغاء
    const buttonCollector = replyMessage.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id && i.customId === 'cancelChangeBanner',
        time: 60000
    });

    buttonCollector.on('collect', async (buttonInteraction) => {
        await buttonInteraction.update({ content: 'تم الغاء العملية.', components: [], embeds: [] });
        buttonCollector.stop();
        collector.stop();
    });

    const filter = m => m.author.id === interaction.user.id && m.attachments.size > 0;
    const collector = interaction.channel.createMessageCollector({ filter, time: 60000 });

    collector.on('collect', async (message) => {
        const imageUrl = message.attachments.first().url;
        let successCount = 0;
        let failedCount = 0;
        let notSupportedCount = 0;

        for (const tokenData of userTokens) {
            const bot = new Client({
                intents: [
                    Intents.FLAGS.GUILDS,
                    Intents.FLAGS.GUILD_MESSAGES
                ]
            });

            try {
                await bot.login(tokenData.token);

                // التحقق من نوع البوت أولاً
                if (bot.user.bot) {
                    try {
                        // استخدام طريقة مباشرة لتغيير البانر عبر API
                        try {
                            // تنزيل الصورة
                            const response = await fetch(imageUrl);
                            if (!response.ok) {
                                throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
                            }
                            const buffer = await response.arrayBuffer();
                            const contentType = message.attachments.first().contentType || 'image/png';
                            const base64Image = `data:${contentType};base64,${Buffer.from(buffer).toString('base64')}`;

                            // استخدام طلب HTTP مباشر إلى Discord API
                            const https = require('https');

                            // إنشاء طلب HTTP
                            const options = {
                                hostname: 'discord.com',
                                path: '/api/v9/users/@me',
                                method: 'PATCH',
                                headers: {
                                    'Authorization': `Bot ${tokenData.token}`,
                                    'Content-Type': 'application/json',
                                    'User-Agent': 'DiscordBot (https://github.com/discordjs/discord.js, 13.0.0)'
                                }
                            };

                            // إرسال الطلب باستخدام Promise للتعامل مع الاستجابة بشكل أفضل
                            await new Promise((resolve, reject) => {
                                const req = https.request(options, (res) => {
                                    let data = '';
                                    res.on('data', (chunk) => {
                                        data += chunk;
                                    });

                                    res.on('end', () => {
                                        if (res.statusCode === 200 || res.statusCode === 204) {
                                            console.log(`[نجاح] تم تغيير البانر للبوت ${bot.user.tag}`);
                                            resolve();
                                        } else {
                                            console.error(`[فشل] ${bot.user.tag}: ${res.statusCode} ${data}`);
                                            if (res.statusCode === 400) {
                                                reject(new Error(`خطأ في الصورة: تأكد من أن حجم الصورة مناسب وأن التنسيق صحيح`));
                                            } else if (res.statusCode === 401 || res.statusCode === 403) {
                                                reject(new Error(`غير مصرح: البوت لا يملك صلاحيات كافية لتغيير البانر`));
                                            } else {
                                                reject(new Error(`خطأ API: ${res.statusCode} ${data}`));
                                            }
                                        }
                                    });
                                });

                                req.on('error', (error) => {
                                    reject(new Error(`خطأ في الطلب: ${error.message}`));
                                });

                                // إرسال البيانات
                                req.write(JSON.stringify({ banner: base64Image }));
                                req.end();
                            });

                            // انتظار لحظة إضافية للتأكد من تطبيق التغييرات
                            await new Promise(resolve => setTimeout(resolve, 2000));

                        } catch (fetchError) {
                            throw new Error(`Failed to process image: ${fetchError.message}`);
                        }

                        successCount++;
                    } catch (bannerError) {
                        console.error(`[خطأ تغيير البانر] ${bot.user.tag}:`, bannerError.message);

                        if (bannerError.message.includes('Missing Permissions') ||
                            bannerError.message.includes('Unauthorized') ||
                            bannerError.message.includes('Cannot set banner') ||
                            bannerError.message.includes('Missing Access') ||
                            bannerError.message.includes('401') ||
                            bannerError.message.includes('403')) {
                            notSupportedCount++;
                            console.log(`[غير مدعوم] ${bot.user.tag} لا يدعم تغيير البانر`);
                        } else if (bannerError.message.includes('Failed to process image')) {
                            failedCount++;
                            console.error(`[فشل معالجة الصورة] ${bot.user.tag}:`, bannerError.message);
                        } else {
                            failedCount++;
                            console.error(`[فشل] ${bot.user.tag}:`, bannerError.message);
                        }
                    }
                } else {
                    notSupportedCount++;
                    console.log(`[غير مدعوم] ${bot.user.tag} ليس بوت تطبيق`);
                }

                await bot.destroy();
            } catch (err) {
                failedCount++;
                console.error(`[خطأ] ${tokenData.token.substring(0, 10)}...:`, err.message);
            }
        }

        const resultEmbed = new MessageEmbed()
            .setTitle("🖼️ نتائج تغيير بانر البوتات")
            .setDescription(`
                **⚙️ نتائج العملية:**
                > ✅ **تم بنجاح:** ${successCount} بوت
                > ⚠️ **غير مدعوم:** ${notSupportedCount} بوت
                > ❌ **فشل:** ${failedCount} بوت
            `)
            .addFields(
                { 
                    name: '📋 متطلبات البوتات المدعومة', 
                    value: '```diff\n+ البوتات الرسمية المعتمدة (Verified Bots)\n+ البوتات في أقل من 10 سيرفر\n+ البوتات بصلاحيات كافية\n```' 
                },
                { 
                    name: '📝 معلومات مهمة', 
                    value: '• قد يستغرق ظهور البانر لمدة تصل إلى 30 دقيقة\n• بعض البوتات تحتاج ميزات خاصة لعرض البانر\n• حاول تسجيل الخروج وإعادة الدخول لرؤية التغييرات'
                },
                { 
                    name: '🖼️ مواصفات الصورة المثالية', 
                    value: '• **الحجم:** أقل من 10 ميغابايت\n• **التنسيق:** PNG أو JPG\n• **الأبعاد المثالية:** 960×540 بكسل'
                }
            )
            .setColor('#355a5d')
            .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683a1631&is=6838c4b1&hm=e88a178057f0721e8248ea79284b23b103da1051aa1d7974acda838daccf4398&=&format=webp&quality=lossless&width=461&height=461')
            .setFooter({ text: 'Oryn Store | تغيير بانر البوتات', iconURL: client.user.displayAvatarURL() })
            .setTimestamp();

        await interaction.followUp({ embeds: [resultEmbed] });
        collector.stop();
        buttonCollector.stop();
    });

    collector.on('end', () => {
        if (!replyMessage.deleted) {
            replyMessage.edit({ components: [] }).catch(console.error);
        }
    });


      } else if (selectedOption === 'restartAllBots') {
        await interaction.deferReply();
        for (const tokenData of userTokens) {
            const botIntents = new Intents([
                Intents.FLAGS.GUILDS,
                Intents.FLAGS.GUILD_MESSAGES,
                Intents.FLAGS.GUILD_MESSAGE_REACTIONS
            ]);
            const bot = new Client({ intents: botIntents });

            try {
                await bot.login(tokenData.token);
                await bot.destroy();
                const newBotInstance = new Client({ intents: botIntents });
                await newBotInstance.login(tokenData.token);
            } catch (err) {
                console.error(`حدث خطأ أثناء إعادة تشغيل البوت: ${bot.user?.username || "غير معروف"} - ${err.message}`);
            }
        }

        const successEmbed = new MessageEmbed()
            .setTitle("إستخدام ناجح ✅")
            .setDescription(`**تم إعادة تشغيل جميع البوتات بنجاح.**`);

        interaction.followUp({ embeds: [successEmbed] });

      } else if (selectedOption === 'changeBotStatus') {
        await interaction.deferReply();
        const promptEmbed = new MessageEmbed()
            .setDescription(`<@${interaction.user.id}>\nيرجى إدخال الحالة التي تريد تعيينها للبوتات`);

        const cancelButton = new MessageButton()
            .setCustomId('cancelChangeStatus')
            .setLabel('الغاء')
            .setStyle('DANGER');

        const actionRow = new MessageActionRow().addComponents(cancelButton);
        const replyMessage = await interaction.followUp({ embeds: [promptEmbed], components: [actionRow] });

        const messageCollector = interaction.channel.createMessageCollector({
            filter: m => m.author.id === interaction.user.id,
            time: 70000
        });

        messageCollector.on('collect', async (message) => {
            const newStatus = message.content.trim().toLowerCase();
            userTokens.forEach(tokenData => {
                tokenData.status = newStatus;
            });
            fs.writeFileSync('./tokens.json', JSON.stringify(tokens, null, 2));

            for (const tokenData of userTokens) {
                const botIntents = new Intents([
                    Intents.FLAGS.GUILDS,
                    Intents.FLAGS.GUILD_MESSAGES,
                    Intents.FLAGS.GUILD_MESSAGE_REACTIONS
                ]);
                const bot = new Client({ intents: botIntents });
                try {
                    await bot.login(tokenData.token);
                    await bot.user.setPresence({
                        activities: [{
                            name: newStatus,
                            type: 'STREAMING',
                            url: "https://twitch.tv/" + newStatus,
                        }],
                        status: newStatus,
                    });
                    await bot.destroy();
                } catch (err) {
                    console.error(`حدث خطأ أثناء تغيير حالة البوت: ${bot.user?.username || "غير معروف"} - ${err.message}`);
                }
            }

            const successEmbed = new MessageEmbed()
                .setTitle("إستخدام ناجح ✅")
                .setDescription(`**تم تغيير حالة جميع البوتات بنجاح**.`);
            await replyMessage.reply({ embeds: [successEmbed] });
            await replyMessage.edit({ components: [] });
            messageCollector.stop();
        });

      } else if (selectedOption === 'transferOwnership') {
        await interaction.deferReply();
        const promptEmbed = new MessageEmbed()
            .setDescription(`<@${interaction.user.id}>\nيرجى عمل منشن (@) للشخص الذي تريد نقل الملكية إليه`);

        const cancelButton = new MessageButton()
            .setCustomId('cancelTransfer')
            .setLabel('الغاء')
            .setStyle('DANGER');

        const actionRow = new MessageActionRow().addComponents(cancelButton);
        const replyMessage = await interaction.followUp({ embeds: [promptEmbed], components: [actionRow] });

        const messageCollector = interaction.channel.createMessageCollector({
            filter: m => m.author.id === interaction.user.id && m.mentions.users.size > 0,
            time: 60000
        });

        messageCollector.on('collect', async (message) => {
            const newOwner = message.mentions.users.first();
            for (const token of userTokens) {
                token.client = newOwner.id;
            }

            try {
                fs.writeFileSync('./tokens.json', JSON.stringify(tokens, null, 2));
                const successEmbed = new MessageEmbed()
                    .setTitle("✅ تم نقل الملكية بنجاح")
                    .setDescription(`تم نقل ملكية ${userTokens.length} بوت إلى <@${newOwner.id}>`)
                    .setColor('#355a5d');
                await interaction.followUp({ embeds: [successEmbed] });

                try {
                    const newOwnerEmbed = new MessageEmbed()
                        .setTitle("🎉 أصبحت مالكًا لـ بوتات جديدة")
                        .setDescription(`تم نقل ملكية ${userTokens.length} بوت إليك من <@${interaction.user.id}>`)
                        .setColor('#355a5d');
                    await newOwner.send({ embeds: [newOwnerEmbed] });
                } catch (err) {
                    console.error('فشل إرسال إشعار للمالك الجديد:', err);
                }
            } catch (error) {
                console.error('حدث خطأ أثناء حفظ التغييرات:', error);
                await interaction.followUp({ content: '❌ حدث خطأ أثناء محاولة نقل الملكية' });
            }
            messageCollector.stop();
        });
      }
    });
  }
}