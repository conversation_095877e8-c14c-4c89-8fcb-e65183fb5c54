2025-06-10T05:19:18.124+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : Starting Launcher v4.1.1 using Java 21.0.7 with PID 8696 (C:\Users\<USER>\Desktop\EW1Wض\Lavalink.jar started by 3nED in C:\Users\<USER>\Desktop\EW1Wض)
2025-06-10T05:19:18.134+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : No active profile set, falling back to 1 default profile: "default"
2025-06-10T05:19:18.645+03:00  INFO 8696 --- [Lavalink] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3d6bf653-92ea-3fde-943a-10b9f8cd696e
2025-06-10T05:19:18.946+03:00  INFO 8696 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Downloading https://maven.lavalink.dev/releases/com/github/topi314/lavasrc/lavasrc-plugin/4.0.1/lavasrc-plugin-4.0.1.jar
2025-06-10T05:19:21.888+03:00  WARN 8696 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : A newer version of 'lavasrc-plugin' was found: '4.7.0', The current version is '4.0.1' please update the version in your configuration.
2025-06-10T05:19:21.888+03:00  INFO 8696 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Downloading https://maven.lavalink.dev/releases/com/github/topi314/lavasearch/lavasearch-plugin/1.0.0/lavasearch-plugin-1.0.0.jar
2025-06-10T05:19:22.726+03:00  INFO 8696 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Loaded 'lavasearch-plugin-1.0.0.jar' (20 classes)
2025-06-10T05:19:22.803+03:00  INFO 8696 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Loaded 'lavasrc-plugin-4.0.1.jar' (123 classes)
2025-06-10T05:19:23.004+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : Started Launcher in 5.328 seconds (process running for 5.763)
2025-06-10T05:19:23.088+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : 

[32m       .  [31m _                  _ _       _    [32m__ _ _
[32m      /\\ [31m| | __ ___   ____ _| (_)_ __ | | __[32m\ \ \ \
[32m     ( ( )[31m| |/ _` \ \ / / _` | | | '_ \| |/ /[32m \ \ \ \
[32m      \\/ [31m| | (_| |\ V / (_| | | | | | |   < [32m  ) ) ) )
[32m       '  [31m|_|\__,_| \_/ \__,_|_|_|_| |_|_|\_\[32m / / / /
[0m    =========================================[32m/_/_/_/[0m

	Version:        4.1.1
	Build time:     05.06.2025 07:37:42 UTC
	Branch          HEAD
	Commit:         4238202
	Commit time:    05.06.2025 07:11:37 UTC
	JVM:            21.0.7
	Lavaplayer      2.2.3

2025-06-10T05:19:23.102+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : No active profile set, falling back to 1 default profile: "default"
2025-06-10T05:19:23.707+03:00  WARN 8696 --- [Lavalink] [main] io.undertow.websockets.jsr               : UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-10T05:19:23.727+03:00  INFO 8696 --- [Lavalink] [main] io.undertow.servlet                      : Initializing Spring embedded WebApplicationContext
2025-06-10T05:19:23.727+03:00  INFO 8696 --- [Lavalink] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 620 ms
2025-06-10T05:19:23.801+03:00  INFO 8696 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Loading LavaSrc plugin...
2025-06-10T05:19:23.825+03:00  INFO 8696 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Registering Spotify search manager...
2025-06-10T05:19:23.825+03:00  INFO 8696 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Registering Youtube search manager...
2025-06-10T05:19:23.857+03:00  INFO 8696 --- [Lavalink] [main] c.s.d.l.tools.GarbageCollectionMonitor   : GC monitoring enabled, reporting results every 2 minutes.
2025-06-10T05:19:23.864+03:00  WARN 8696 --- [Lavalink] [main] l.s.config.AudioPlayerConfiguration      : The default Youtube source is now deprecated and won't receive further updates.
You should use the new Youtube source plugin instead.
https://github.com/lavalink-devs/youtube-source#plugin.
To disable this warning, set 'lavalink.server.sources.youtube' to false in your application.yml.
2025-06-10T05:19:24.248+03:00  INFO 8696 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Registering Spotify audio source manager...
2025-06-10T05:19:24.402+03:00  INFO 8696 --- [Lavalink] [main] l.server.config.KoeConfiguration         : OS: WINDOWS, Arch: X86_64
2025-06-10T05:19:24.402+03:00  INFO 8696 --- [Lavalink] [main] l.server.config.KoeConfiguration         : Enabling JDA-NAS
2025-06-10T05:19:24.405+03:00  INFO 8696 --- [Lavalink] [main] c.s.l.c.natives.NativeLibraryLoader      : Native library udpqueue: loading with filter null
2025-06-10T05:19:24.408+03:00  INFO 8696 --- [Lavalink] [main] c.s.l.c.natives.NativeLibraryLoader      : Native library udpqueue: successfully loaded.
2025-06-10T05:19:24.433+03:00  WARN 8696 --- [Lavalink] [main] l.server.config.SentryConfiguration      : Turning off sentry
2025-06-10T05:19:24.628+03:00  INFO 8696 --- [Lavalink] [main] io.undertow                              : starting server: Undertow - 2.3.13.Final
2025-06-10T05:19:24.635+03:00  INFO 8696 --- [Lavalink] [main] org.xnio                                 : XNIO version 3.8.8.Final
2025-06-10T05:19:24.643+03:00  INFO 8696 --- [Lavalink] [main] org.xnio.nio                             : XNIO NIO Implementation Version 3.8.8.Final
2025-06-10T05:19:24.716+03:00  INFO 8696 --- [Lavalink] [main] org.jboss.threads                        : JBoss Threads version 3.5.0.Final
2025-06-10T05:19:24.797+03:00  INFO 8696 --- [Lavalink] [main] o.s.b.w.e.undertow.UndertowWebServer     : Undertow started on port 2333 (http) with context path '/'
2025-06-10T05:19:24.805+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : Started Launcher in 1.796 seconds (process running for 7.564)
2025-06-10T05:19:24.805+03:00  INFO 8696 --- [Lavalink] [main] lavalink.server.Launcher                 : Lavalink is ready to accept connections.
2025-06-10T05:19:53.217+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] io.undertow.servlet                      : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-10T05:19:53.217+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-10T05:19:53.218+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-10T05:19:53.239+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57832
2025-06-10T05:19:53.254+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.300+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.385+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57837
2025-06-10T05:19:53.386+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.387+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.450+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57842
2025-06-10T05:19:53.451+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.452+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.505+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-5] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57847
2025-06-10T05:19:53.506+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-5] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.508+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-5] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.512+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.512+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.512+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.529+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57851
2025-06-10T05:19:53.530+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.530+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.531+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.534+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.557+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57854
2025-06-10T05:19:53.558+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.559+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.561+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.590+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57857
2025-06-10T05:19:53.592+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.593+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.595+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.694+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57862
2025-06-10T05:19:53.695+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.697+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.699+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.740+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57865
2025-06-10T05:19:53.740+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57866
2025-06-10T05:19:53.741+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.741+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.741+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.741+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.743+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.743+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.757+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57870
2025-06-10T05:19:53.758+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.759+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.760+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.780+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57873
2025-06-10T05:19:53.781+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.782+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.783+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.790+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57876
2025-06-10T05:19:53.791+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.792+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.794+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57880
2025-06-10T05:19:53.827+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.828+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.830+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.854+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57883
2025-06-10T05:19:53.855+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.856+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.857+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.865+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57885
2025-06-10T05:19:53.865+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.866+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.868+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:53.894+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57887
2025-06-10T05:19:53.895+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:53.896+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:53.898+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:54.121+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57891
2025-06-10T05:19:54.122+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:54.123+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:54.125+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:54.151+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57894
2025-06-10T05:19:54.151+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:54.152+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:54.153+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57896
2025-06-10T05:19:54.153+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:54.154+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:54.154+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:54.156+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:54.563+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.HandshakeInterceptorImpl     : Incoming connection from /127.0.0.1:57902
2025-06-10T05:19:54.564+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/websocket, client=127.0.0.1
2025-06-10T05:19:54.565+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] lavalink.server.io.SocketServer          : Connection successfully established from Moonlink.js/4.6.3 (https://github.com/Ecliptia/moonlink.js)
2025-06-10T05:19:54.566+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : GET /v4/info, client=127.0.0.1
2025-06-10T05:19:58.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-16] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/9vkqvygrm42ooqil/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"c5efb5d5ed42d28562b223263f280134","token":"24618841c4c7bcb2","endpoint":"frankfurt6770.discord.media:443"}}
2025-06-10T05:19:58.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-13] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/72yekmv1aljpeh0t/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"c44fad8a2733728a94282a001f821043","token":"3def57de4e25d274","endpoint":"frankfurt2803.discord.media:443"}}
2025-06-10T05:19:58.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-6] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/4w5g500sx7msqczx/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"6cdf50c63634e3863451d69872b05795","token":"99c62c81aeedc838","endpoint":"frankfurt738.discord.media:443"}}
2025-06-10T05:19:58.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-8] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/rn81d0orur8mg8jw/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"c4018d6581b546d830ad97cf9cbcef1d","token":"3862ecdb223c73a1","endpoint":"frankfurt4492.discord.media:443"}}
2025-06-10T05:19:58.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-2] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/wuqf9sazvtp5m38v/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"47525c47581d9c5f2e5cadacd321a3a6","token":"1dc0fe2020b30f0b","endpoint":"frankfurt882.discord.media:443"}}
2025-06-10T05:19:58.826+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-14] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/y3m17k9apohiviv3/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"052a1685534a51e916296d679a636af0","token":"48e59438eafb286f","endpoint":"frankfurt3916.discord.media:443"}}
2025-06-10T05:19:58.829+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-10] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/frlu29cw82awo0g8/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"b9f2638d3fa2b56e2aa52c85e9005c69","token":"1e60f2acc08cb012","endpoint":"frankfurt6526.discord.media:443"}}
2025-06-10T05:19:58.829+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-5] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/zb3grgk1eiuzpefk/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"1e757c142cbedd3c769bc7d48d850345","token":"361037f068fa9bfa","endpoint":"frankfurt6770.discord.media:443"}}
2025-06-10T05:19:58.829+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-15] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/ywsshinkain5jtqn/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"74470f2ca5d64d8ff2fa02b8c982c977","token":"04d24ef3e75a07bd","endpoint":"frankfurt4480.discord.media:443"}}
2025-06-10T05:19:58.839+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-18] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/qixtfttpzcszpmux/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"29be18a3105dab7627a8f565313f8012","token":"523c9d8b9470d952","endpoint":"frankfurt2951.discord.media:443"}}
2025-06-10T05:19:58.839+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-3] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/1sq62ktd1j17o8bs/players/1326302372523147396, client=127.0.0.1, payload={"voice":{"sessionId":"616f49fbc7adf15db6897ec892184a77","token":"ded1116a1cd083fc","endpoint":"frankfurt487.discord.media:443"}}
2025-06-10T05:19:58.839+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-7] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/uscwgl4ocp54pp7h/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"6f485577415b469a49606c48e0fc9b73","token":"91184574ab8e342d","endpoint":"frankfurt5219.discord.media:443"}}
2025-06-10T05:19:58.839+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-17] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/rjx35oujmj8xph6l/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"28e23dc10984bbbe560362946c2c5704","token":"fdb0ca4d99981aeb","endpoint":"frankfurt8784.discord.media:443"}}
2025-06-10T05:19:58.839+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-12] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/61itt1bj67i6ffbr/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"40f191bcfafcbbbeea8a6629c1c6da08","token":"15ffcea98e0f8d8a","endpoint":"frankfurt5081.discord.media:443"}}
2025-06-10T05:19:58.839+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-4] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/3pj31614k4xzoxmk/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"d81a8e9e0eb2a25d0956fdeedefc411a","token":"ec7a6b6654cb2b00","endpoint":"frankfurt2336.discord.media:443"}}
2025-06-10T05:19:58.892+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-11] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/su0kvx9sdzejjqbh/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"8e73344a3753c53b3f2b6826fe3d427d","token":"fba12dc1ed8a410a","endpoint":"frankfurt4785.discord.media:443"}}
2025-06-10T05:19:58.896+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-9] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/df5o85ep4uj13y92/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"5197c1a02ef4b675eaf229213f55c121","token":"faec246a44d9765e","endpoint":"frankfurt829.discord.media:443"}}
2025-06-10T05:19:58.966+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-19] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/uy5o6i6lo4jtw5xg/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"a1bb9eda5706a956d54b8a7924fa5faa","token":"055a0d71943f2fcc","endpoint":"frankfurt1078.discord.media:443"}}
2025-06-10T05:19:59.046+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-20] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/o3dzjpndidsn178n/players/1060649709531840572, client=127.0.0.1, payload={"voice":{"sessionId":"6528657843a21efb1638fe4b60a303d5","token":"eea8a23ccbaec32b","endpoint":"frankfurt4488.discord.media:443"}}
2025-06-10T05:19:59.617+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-20] l.server.io.RequestLoggingFilter         : PATCH /v4/sessions/glqg5ygzqbcaocj5/players/1275942144401735820, client=127.0.0.1, payload={"voice":{"sessionId":"2dce1a58ac1fe92058b5a897432f3fa3","token":"028fd7302c0cd4b3","endpoint":"frankfurt6482.discord.media:443"}}
2025-06-10T05:20:09.209+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-20] l.server.player.AudioLoaderRestHandler   : Got request to load for identifier "ytsearch:قران"
2025-06-10T05:20:09.646+03:00 ERROR 8696 --- [Lavalink] [XNIO-1 task-20] l.server.player.AudioLoaderRestHandler   : Failed to load track for identifier ytsearch:قران

com.sedmelluq.discord.lavaplayer.tools.FriendlyException: Something went wrong while looking up the track.
	at lavalink.server.util.LoadingKt.loadAudioItem(loading.kt:20) ~[classes!/:4.1.1]
	at lavalink.server.player.AudioLoaderRestHandler.loadTracks(AudioLoaderRestHandler.kt:59) ~[classes!/:4.1.1]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method.callMethod(CallerImpl.kt:97) ~[kotlin-reflect-2.1.20.jar!/:2.1.20-release-217]
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method$Instance.call(CallerImpl.kt:113) ~[kotlin-reflect-2.1.20.jar!/:2.1.20-release-217]
	at kotlin.reflect.jvm.internal.KCallableImpl.callDefaultMethod$kotlin_reflection(KCallableImpl.kt:207) ~[kotlin-reflect-2.1.20.jar!/:2.1.20-release-217]
	at kotlin.reflect.jvm.internal.KCallableImpl.callBy(KCallableImpl.kt:112) ~[kotlin-reflect-2.1.20.jar!/:2.1.20-release-217]
	at org.springframework.web.method.support.InvocableHandlerMethod$KotlinDelegate.invokeFunction(InvocableHandlerMethod.java:334) ~[spring-web-6.1.15.jar!/:6.1.15]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:252) ~[spring-web-6.1.15.jar!/:6.1.15]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188) ~[spring-web-6.1.15.jar!/:6.1.15]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527) ~[jakarta.servlet-api-6.0.0.jar!/:6.0.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.1.8.jar!/:6.1.8]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614) ~[jakarta.servlet-api-6.0.0.jar!/:6.0.0]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) ~[spring-web-6.1.15.jar!/:6.1.15]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar!/:6.1.15]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at lavalink.server.io.ResponseHeaderFilter.doFilterInternal(ResponseHeaderFilter.kt:17) ~[classes!/:4.1.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar!/:6.1.15]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.1.15.jar!/:6.1.15]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.1.15.jar!/:6.1.15]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101) ~[undertow-servlet-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859) ~[undertow-core-2.3.13.Final.jar!/:2.3.13.Final]
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18) ~[jboss-threads-3.5.0.Final.jar!/:3.5.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513) ~[jboss-threads-3.5.0.Final.jar!/:3.5.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538) ~[jboss-threads-3.5.0.Final.jar!/:3.5.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) ~[xnio-api-3.8.8.Final.jar!/:3.8.8.Final]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: java.lang.RuntimeException: java.io.IOException: Invalid status code for search response: 400
	at com.sedmelluq.discord.lavaplayer.tools.ExceptionTools.wrapUnfriendlyExceptions(ExceptionTools.java:59) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.YoutubeSearchProvider.loadSearchResult(YoutubeSearchProvider.java:71) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.YoutubeAudioSourceManager$LoadingRoutes.search(YoutubeAudioSourceManager.java:295) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.YoutubeAudioSourceManager$LoadingRoutes.search(YoutubeAudioSourceManager.java:261) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.DefaultYoutubeLinkRouter.route(DefaultYoutubeLinkRouter.java:39) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.YoutubeAudioSourceManager.loadItemOnce(YoutubeAudioSourceManager.java:229) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.YoutubeAudioSourceManager.loadItem(YoutubeAudioSourceManager.java:160) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.player.DefaultAudioPlayerManager.checkSourcesForItemOnce(DefaultAudioPlayerManager.java:442) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.player.DefaultAudioPlayerManager.checkSourcesForItem(DefaultAudioPlayerManager.java:423) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.player.DefaultAudioPlayerManager.loadItemSync(DefaultAudioPlayerManager.java:154) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.player.AudioPlayerManager.loadItemSync(AudioPlayerManager.java:127) ~[lavaplayer-2.2.3.jar!/:na]
	at lavalink.server.util.LoadingKt.loadAudioItem(loading.kt:15) ~[classes!/:4.1.1]
	... 65 common frames omitted
Caused by: java.io.IOException: Invalid status code for search response: 400
	at com.sedmelluq.discord.lavaplayer.tools.io.HttpClientTools.assertSuccessWithContent(HttpClientTools.java:168) ~[lavaplayer-2.2.3.jar!/:na]
	at com.sedmelluq.discord.lavaplayer.source.youtube.YoutubeSearchProvider.loadSearchResult(YoutubeSearchProvider.java:63) ~[lavaplayer-2.2.3.jar!/:na]
	... 75 common frames omitted

2025-06-10T05:20:09.666+03:00  INFO 8696 --- [Lavalink] [XNIO-1 task-20] l.server.io.RequestLoggingFilter         : GET /v4/loadtracks?identifier=ytsearch%3A%D9%82%D8%B1%D8%A7%D9%86, client=127.0.0.1
2025-06-10T05:21:56.070+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : Starting Launcher v4.1.1 using Java 21.0.7 with PID 14244 (C:\Users\<USER>\Desktop\EW1Wض\Lavalink.jar started by 3nED in C:\Users\<USER>\Desktop\EW1Wض)
2025-06-10T05:21:56.079+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : No active profile set, falling back to 1 default profile: "default"
2025-06-10T05:21:56.591+03:00  INFO 14244 --- [Lavalink] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3d6bf653-92ea-3fde-943a-10b9f8cd696e
2025-06-10T05:21:56.901+03:00  INFO 14244 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Found plugin 'lavasearch-plugin' version '1.0.0'
2025-06-10T05:21:56.901+03:00  INFO 14244 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Found plugin 'lavasrc-plugin' version '4.0.1'
2025-06-10T05:21:57.955+03:00  WARN 14244 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : A newer version of 'lavasrc-plugin' was found: '4.7.0', The current version is '4.0.1' please update the version in your configuration.
2025-06-10T05:21:58.187+03:00  INFO 14244 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Loaded 'lavasearch-plugin-1.0.0.jar' (20 classes)
2025-06-10T05:21:58.270+03:00  INFO 14244 --- [Lavalink] [main] l.server.bootstrap.PluginManager         : Loaded 'lavasrc-plugin-4.0.1.jar' (123 classes)
2025-06-10T05:21:58.463+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : Started Launcher in 2.842 seconds (process running for 3.279)
2025-06-10T05:21:58.532+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : 

[32m       .  [31m _                  _ _       _    [32m__ _ _
[32m      /\\ [31m| | __ ___   ____ _| (_)_ __ | | __[32m\ \ \ \
[32m     ( ( )[31m| |/ _` \ \ / / _` | | | '_ \| |/ /[32m \ \ \ \
[32m      \\/ [31m| | (_| |\ V / (_| | | | | | |   < [32m  ) ) ) )
[32m       '  [31m|_|\__,_| \_/ \__,_|_|_|_| |_|_|\_\[32m / / / /
[0m    =========================================[32m/_/_/_/[0m

	Version:        4.1.1
	Build time:     05.06.2025 07:37:42 UTC
	Branch          HEAD
	Commit:         4238202
	Commit time:    05.06.2025 07:11:37 UTC
	JVM:            21.0.7
	Lavaplayer      2.2.3

2025-06-10T05:21:58.541+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : No active profile set, falling back to 1 default profile: "default"
2025-06-10T05:21:59.082+03:00  WARN 14244 --- [Lavalink] [main] io.undertow.websockets.jsr               : UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-10T05:21:59.099+03:00  INFO 14244 --- [Lavalink] [main] io.undertow.servlet                      : Initializing Spring embedded WebApplicationContext
2025-06-10T05:21:59.099+03:00  INFO 14244 --- [Lavalink] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 553 ms
2025-06-10T05:21:59.168+03:00  INFO 14244 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Loading LavaSrc plugin...
2025-06-10T05:21:59.190+03:00  INFO 14244 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Registering Spotify search manager...
2025-06-10T05:21:59.191+03:00  INFO 14244 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Registering Youtube search manager...
2025-06-10T05:21:59.223+03:00  INFO 14244 --- [Lavalink] [main] c.s.d.l.tools.GarbageCollectionMonitor   : GC monitoring enabled, reporting results every 2 minutes.
2025-06-10T05:21:59.229+03:00  WARN 14244 --- [Lavalink] [main] l.s.config.AudioPlayerConfiguration      : The default Youtube source is now deprecated and won't receive further updates.
You should use the new Youtube source plugin instead.
https://github.com/lavalink-devs/youtube-source#plugin.
To disable this warning, set 'lavalink.server.sources.youtube' to false in your application.yml.
2025-06-10T05:21:59.628+03:00  INFO 14244 --- [Lavalink] [main] c.g.t.lavasrc.plugin.LavaSrcPlugin       : Registering Spotify audio source manager...
2025-06-10T05:21:59.775+03:00  INFO 14244 --- [Lavalink] [main] l.server.config.KoeConfiguration         : OS: WINDOWS, Arch: X86_64
2025-06-10T05:21:59.775+03:00  INFO 14244 --- [Lavalink] [main] l.server.config.KoeConfiguration         : Enabling JDA-NAS
2025-06-10T05:21:59.778+03:00  INFO 14244 --- [Lavalink] [main] c.s.l.c.natives.NativeLibraryLoader      : Native library udpqueue: loading with filter null
2025-06-10T05:21:59.783+03:00  INFO 14244 --- [Lavalink] [main] c.s.l.c.natives.NativeLibraryLoader      : Native library udpqueue: successfully loaded.
2025-06-10T05:21:59.809+03:00  WARN 14244 --- [Lavalink] [main] l.server.config.SentryConfiguration      : Turning off sentry
2025-06-10T05:22:00.009+03:00  INFO 14244 --- [Lavalink] [main] io.undertow                              : starting server: Undertow - 2.3.13.Final
2025-06-10T05:22:00.018+03:00  INFO 14244 --- [Lavalink] [main] org.xnio                                 : XNIO version 3.8.8.Final
2025-06-10T05:22:00.025+03:00  INFO 14244 --- [Lavalink] [main] org.xnio.nio                             : XNIO NIO Implementation Version 3.8.8.Final
2025-06-10T05:22:00.105+03:00  INFO 14244 --- [Lavalink] [main] org.jboss.threads                        : JBoss Threads version 3.5.0.Final
2025-06-10T05:22:00.176+03:00  INFO 14244 --- [Lavalink] [main] o.s.b.w.e.undertow.UndertowWebServer     : Undertow started on port 2333 (http) with context path '/'
2025-06-10T05:22:00.184+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : Started Launcher in 1.716 seconds (process running for 5.0)
2025-06-10T05:22:00.185+03:00  INFO 14244 --- [Lavalink] [main] lavalink.server.Launcher                 : Lavalink is ready to accept connections.
