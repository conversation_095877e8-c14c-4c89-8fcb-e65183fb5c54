@echo off
echo Starting Lavalink server...

REM Check if Lavalink.jar exists
if not exist "Lavalink.jar" (
    echo Lavalink.jar not found. Please download it from:
    echo https://github.com/lavalink-devs/Lavalink/releases
    echo.
    echo Download the latest Lavalink.jar and place it in this directory.
    pause
    exit /b 1
)

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Java is not installed or not in PATH.
    echo Please install Java 17 or higher.
    pause
    exit /b 1
)

REM Start Lavalink
echo Starting Lavalink with configuration file...
java -jar Lavalink.jar

pause
