{"name": "node.js", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pm2 start index.js --name ss_z", "install-deps": "npm install node-schedule"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@discordjs/opus": "^0.8.0", "@discordjs/voice": "^0.18.0", "@types/node": "^18.0.6", "all": "^0.0.0", "axios": "^1.3.4", "canvas": "^2.11.2", "canvas-constructor": "^7.0.1", "child_process": "^1.0.2", "discord.js": "^13.12.0", "express": "^4.18.2", "ffmpeg-static": "^5.1.0", "install": "^0.13.0", "libsodium-wrappers": "^0.7.15", "moonlink.js": "^4.6.3", "ms": "^2.1.3", "node-cron": "^3.0.3", "node-fetch": "^2.6.7", "node-opus": "^0.3.3", "node-schedule": "^2.1.1", "npm": "^9.8.1", "opusscript": "^0.0.8", "pm2": "^5.3.1", "pro.db": "^3.0.8", "random-words": "^2.0.0", "totp-generator": "^0.0.14", "tweetnacl": "^1.0.3", "ytdl-core": "^4.11.5", "ytsr": "^3.8.4"}, "devDependencies": {"husky": "^9.1.7"}}