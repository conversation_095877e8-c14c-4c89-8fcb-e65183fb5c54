import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
import type { Snowflake } from '../../../../../globals';
export declare type APIApplicationCommandAttachmentOption = APIApplicationCommandOptionBase<ApplicationCommandOptionType.Attachment>;
export declare type APIApplicationCommandInteractionDataAttachmentOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.Attachment, Snowflake>;
//# sourceMappingURL=attachment.d.ts.map