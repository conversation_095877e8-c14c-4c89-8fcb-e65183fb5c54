const fs = require('fs');
const ms = require('ms');
const { owners, prefix, emco } = require(`${process.cwd()}/config`);
const { MessageEmbed } = require('discord.js');

// دالة للتأكد من سلامة ملف السجلات
function ensureLogsFileExists() {
  try {
    if (!fs.existsSync('./logs.json')) {
      fs.writeFileSync('./logs.json', '[]', 'utf8');
      console.log('تم إنشاء ملف logs.json جديد');
      return true;
    }
    
    const content = fs.readFileSync('./logs.json', 'utf8');
    if (!content || content.trim() === '') {
      fs.writeFileSync('./logs.json', '[]', 'utf8');
      console.log('تم إعادة تهيئة ملف logs.json الفارغ');
      return true;
    }
    
    try {
      JSON.parse(content);
      return true;
    } catch (error) {
      console.error('ملف logs.json غير صالح، تتم إعادة تهيئته:', error);
      fs.writeFileSync('./logs.json', '[]', 'utf8');
      return true;
    }
  } catch (error) {
    console.error('حدث خطأ أثناء التحقق من ملف logs.json:', error);
    return false;
  }
}

module.exports = {
  name: 'mysub',
  aliases: ["اشتراك"],
  run: async (client, message, args) => {
    let userId;

    // Check if the user mentioned someone or provided a user ID
    if (message.mentions.users.size > 0) {
      userId = message.mentions.users.first().id;
    } else if (args[0]) {
      userId = args[0];
    } else {
      // If neither mentioned nor provided an ID, default to the message author
      userId = message.author.id;
    }
    
    try {
      // التحقق من وجود الملف وصحته
      if (!ensureLogsFileExists()) {
        return message.reply('**حدث خطأ أثناء التحقق من ملف السجلات. الرجاء المحاولة لاحقًا.**');
      }
      
      const logs = fs.readFileSync('./logs.json', 'utf8');
      // تحليل محتوى الملف بشكل آمن
      let logsArray = [];
      try {
        logsArray = JSON.parse(logs);
        if (!Array.isArray(logsArray)) {
          throw new Error('تنسيق البيانات غير صحيح');
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
        fs.writeFileSync('./logs.json', '[]', 'utf8');
        message.reply('**تم إصلاح ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        return;
      }

      const userSubscriptions = logsArray.filter(entry => entry.user === userId);

      if (userSubscriptions.length === 0) {
        return message.reply('**لا يوجد لديك أي اشتراك .**');
      }

      const embed = new MessageEmbed()
        .setTitle('Music Subscriptions')
        .setColor(emco)
        .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
        .setFooter(`${message.client.user.username} | Timer`, `${message.client.user.displayAvatarURL({ dynamic: true })}`);

      userSubscriptions.forEach((userSubscription, index) => {
        const expirationTime = userSubscription.expirationTime;
        const remainingTime = expirationTime - Date.now();

        // حساب الأيام والساعات والدقائق والثواني المتبقية
        const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
        const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

        const formattedTime = `${days ? `${days}d ` : ''}${hours ? `${hours}h ` : ''}${minutes ? `${minutes}m ` : ''}${seconds ? `${seconds}s` : ''}`;
        embed.setDescription(`${embed.description || ''}\n**\`${index + 1}\` | \`Music x${userSubscription.botsCount}\` | \`${userSubscription.code}\` | ${formattedTime}**`);
      });

      // إرسال رسالة الرد ك Embed مع الوصف
      message.reply({ embeds: [embed] });    } catch (error) {
      console.error('❌> خطأ في أمر mysub:', error);
      
      // تحديد نوع الخطأ والتعامل معه بشكل مناسب
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        console.error('❌> خطأ في تحليل بيانات JSON:', error);
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          message.reply('**تم إصلاح ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        } catch (writeError) {
          console.error('❌> فشل في إصلاح ملف السجلات:', writeError);
          message.reply('**حدث خطأ أثناء محاولة إصلاح ملف السجلات. الرجاء الاتصال بمسؤول النظام.**');
        }
      } else if (error.code === 'ENOENT') {
        // خطأ عدم وجود الملف
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          message.reply('**تم إنشاء ملف السجلات. الرجاء إعادة تنفيذ الأمر.**');
        } catch (writeError) {
          console.error('❌> فشل في إنشاء ملف السجلات:', writeError);
          message.reply('**حدث خطأ أثناء محاولة إنشاء ملف السجلات. الرجاء الاتصال بمسؤول النظام.**');
        }
      } else {
        // أخطاء أخرى غير متوقعة
        message.reply('**حدث خطأ أثناء قراءة بيانات الاشتراكات. الرجاء المحاولة لاحقًا.**');
      }
    }
  }
};
