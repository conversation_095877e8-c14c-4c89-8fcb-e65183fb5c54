{"name": "moonlink.js", "version": "2.16.60", "description": "Imagine a Music... Welcome to MoonLink! A sample npm maked in javascript to your create your discord music bot using lavalink!", "keywords": ["bot", "discord.js", "moonlink", "discord", "easy", "lavalink", "music"], "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"clean": "rm -rf dist", "build": "tsc", "build:esm": "gen-esm-wrapper dist/index.js dist/index.mjs", "formater": "prettier . --write", "start": "npm run clean && npm run formater && npm run build && npm run build:esm && node testBot/index.js"}, "homepage": "https://moonlink.js.org", "bugs": {"url": "https://github.com/1Lucas1apk/moonlink.js/issues", "email": "<EMAIL>"}, "license": {"type": "Apache-2.0", "url": "https://opensource.org/licenses/apache2.0.php"}, "contributors": ["1Lucas1.apk", "motoG.js"], "repository": {"type": "git", "url": "https://github.com/1Lucas1apk/moonlink.js"}, "engines": {"node": ">=18.8.0"}, "devDependencies": {"@types/node": "^18.15.11", "bun": "^1.0.13", "discord.js": "^14.11.0", "gen-esm-wrapper": "^1.1.3", "get-image-colors": "^4.0.1", "prettier": "^3.0.3", "typescript": "^5.2.2"}}