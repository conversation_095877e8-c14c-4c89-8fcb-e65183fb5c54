{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACX,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,SAAS,EACT,UAAU,EACV,mBAAmB,EACnB,YAAY,EACZ,OAAO,EACP,WAAW,EACX,oBAAoB,EACpB,YAAY,EACZ,aAAa,EACb,MAAM,yBAAyB,CAAC;AAIjC;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,aAAa,CAAC;IACpB,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;CACtB;AAED;;;GAGG;AACH,oBAAY,oBAAoB;IAC/B,QAAQ,aAAa;IACrB,IAAI,UAAU;IACd,IAAI,UAAU;CACd;AAED;;;GAGG;AACH,MAAM,WAAW,sBAAsB;IACtC,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC;IAC/B,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;CACjB;AAID;;;GAGG;AACH,MAAM,WAAW,2BAA2B;IAC3C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;IACvD,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACtB,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,qBAAqB,CAAC,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IAC9C,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1B;AAED;;GAEG;AACH,oBAAY,uBAAuB,GAAG,UAAU,CAAC;AAEjD;;GAEG;AACH,oBAAY,yBAAyB,GAAG,UAAU,CAAC;AAEnD;;GAEG;AACH,oBAAY,0BAA0B,GAAG,UAAU,CAAC;AAEpD;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAC9C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,+BAA+B,GAAG,UAAU,EAAE,CAAC;AAE3D;;;GAGG;AACH,MAAM,WAAW,iCAAiC;IACjD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,GAAG,CAAC,EAAE,OAAO,CAAC;IACd,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB,gBAAgB,CAAC,EAAE,sBAAsB,CAAC;IAC1C,iBAAiB,CAAC,EAAE,mBAAmB,CAAC;CACxC;AAED;;;GAGG;AACH,oBAAY,qCAAqC,GAC9C;IACA;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;CACb,GACD;IACA,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,GAAG,CAAC,EAAE,OAAO,CAAC;IACd,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB,gBAAgB,CAAC,EAAE,sBAAsB,CAAC;IAC1C,iBAAiB,CAAC,EAAE,mBAAmB,CAAC;IACxC;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;CACb,CAAC;AAEL;;;GAGG;AACH,MAAM,WAAW,kCAAkC;IAClD,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC;IACxB,gBAAgB,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;IACjD,KAAK,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC;CAC5B;AAED;;GAEG;AACH,oBAAY,8BAA8B,GAAG,UAAU,CAAC;AAExD;;GAEG;AACH,oBAAY,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,oBAAY,gCAAgC,GAAG,UAAU,CAAC;AAE1D;;GAEG;AACH,oBAAY,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;GAGG;AACH,MAAM,WAAW,sCAAsC;IACtD,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,uCAAuC,GAAG,OAAO,EAAE,CAAC;AAEhE;;GAEG;AACH,oBAAY,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;GAEG;AACH,oBAAY,yCAAyC,GAAG,KAAK,CAAC;AAE9D;;GAEG;AACH,oBAAY,6CAA6C,GAAG,KAAK,CAAC;AAElE;;;GAGG;AACH,MAAM,WAAW,4CAA4C;IAC5D,QAAQ,EAAE,MAAM,EAAE,CAAC;CACnB;AAED;;GAEG;AACH,oBAAY,0CAA0C,GAAG,KAAK,CAAC;AAE/D;;;GAGG;AACH,MAAM,WAAW,oCAAoC;IACpD,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;IACtB,IAAI,EAAE,aAAa,CAAC;CACpB;AAED;;GAEG;AACH,oBAAY,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;GAEG;AACH,oBAAY,qCAAqC,GAAG,KAAK,CAAC;AAE1D;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,SAAS,EAAE,CAAC;AAEzD;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAChD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,gBAAgB,CAAC,EAAE,oBAAoB,CAAC;CACxC;AAED;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;GAGG;AACH,oBAAY,2BAA2B,GAAG,UAAU,EAAE,CAAC;AAEvD;;;GAGG;AACH,oBAAY,0BAA0B,GAAG,KAAK,CAAC;AAE/C;;GAEG;AACH,oBAAY,6BAA6B,GAAG,KAAK,CAAC;AAElD;;;GAGG;AACH,MAAM,WAAW,kCAAkC;IAClD,YAAY,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,oBAAY,gCAAgC,GAAG,OAAO,CAAC;AAEvD;;GAEG;AACH,oBAAY,mCAAmC,GAAG,OAAO,CAAC;AAI1D;;GAEG;AACH,oBAAY,wCAAwC,GAAG,UAAU,CAAC;AAElE;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD,kBAAkB,EAAE,MAAM,CAAC;CAC3B;AAED;;GAEG;AACH,oBAAY,iCAAiC,GAAG,kBAAkB,CAAC"}