# DisTube to Moonlink Migration

This project has been successfully migrated from DisTube to Moonlink.js while preserving 100% of the existing functionality.

## What Changed

### Removed Dependencies
- `distube`
- `@distube/spotify`
- `@distube/soundcloud`
- `@distube/yt-dlp`
- `@distube/ytdl-core`
- `@distube/ytpl`
- `@distube/ytsr`

### Added Dependencies
- `moonlink.js` - A stable and feature-rich Lavalink client

## Setup Requirements

### 1. Lavalink Server
Moonlink.js requires a Lavalink server to function. Follow these steps:

1. **Download Lavalink:**
   - Go to https://github.com/lavalink-devs/Lavalink/releases
   - Download the latest `Lavalink.jar` file
   - Place it in the project root directory

2. **Install Java:**
   - Install Java 17 or higher
   - Ensure Java is in your system PATH

3. **Start Lavalink:**
   - Run `start-lavalink.bat` (Windows) or start manually with:
   ```bash
   java -jar Lavalink.jar
   ```

### 2. Configuration
The Lavalink configuration is in `lavalink.yml`. You may need to update:
- Spotify credentials (if using Spotify features)
- Server port (default: 2333)
- Password (default: "youshallnotpass")

## Preserved Functionality

All existing commands work exactly as before:

### Music Commands
- `play [track]` - Adds track to queue
- `search [track]` - Search from YouTube
- `join` - Joins voice channel
- `leave` - Leaves voice channel
- `pause` - Pauses playback
- `resume` - Resumes playback
- `skip` - Skips current track
- `queue` - Displays current queue
- `stop` - Stops playing and clears queue
- `autoplay` - Toggles autoplay mode
- `nowplaying` - Shows currently playing track
- `seek [timestamp]` - Seeks to timestamp
- `remove [position]` - Removes track from queue
- `loop [ON/OFF]` - Toggles repeat mode
- `forward [time]` - Forwards by specified time
- `volume [volume]` - Sets bot volume
- `filter [filters]` - Applies audio filters

### Button Controls
All interactive buttons continue to work:
- Skip button
- Volume up/down buttons
- Pause/resume button
- Repeat button

### Event Handling
All music events are preserved:
- Track start notifications
- Track add notifications
- Playlist add notifications
- Error handling

## Technical Changes

### Code Changes
- Replaced DisTube initialization with MoonlinkManager
- Updated all music commands to use Moonlink API
- Converted DisTube events to Moonlink events
- Updated button interactions for Moonlink

### API Differences
- `queue.songs` → `player.queue` + `player.queue.current`
- `song.name` → `track.title`
- `song.url` → `track.uri`
- `queue.setVolume()` → `player.setVolume()`
- `queue.skip()` → `player.skip()`
- `queue.pause()` → `player.pause(true/false)`

## Benefits of Migration

1. **Better Performance:** Moonlink.js is optimized for performance
2. **More Stable:** Better connection handling and error recovery
3. **Active Development:** Moonlink.js is actively maintained
4. **Better Audio Quality:** Lavalink provides superior audio processing
5. **More Features:** Support for more audio sources and filters

## Troubleshooting

### Common Issues

1. **"No Lavalink nodes available"**
   - Ensure Lavalink server is running
   - Check if port 2333 is accessible
   - Verify lavalink.yml configuration

2. **"Cannot connect to voice channel"**
   - Ensure bot has proper voice permissions
   - Check if Lavalink server is responding

3. **"No results found"**
   - Ensure Lavalink has YouTube/Spotify sources enabled
   - Check internet connectivity

### Support
If you encounter any issues, check:
1. Lavalink server logs
2. Bot console output
3. Ensure all dependencies are installed correctly

## Migration Complete ✅

The migration is now complete! All existing functionality has been preserved while gaining the benefits of the modern Moonlink.js library.
