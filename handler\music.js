// Music command handlers for moonlink.js
const { MessageEmbed } = require('discord.js');
const { emco, useEmbeds } = require('../config.json');

module.exports = {
  // Play music
  async play(client, message, args) {
    if (!message.member.voice.channel) {
      return message.reply('❌ You need to be in a voice channel to use this command!');
    }

    if (!args.length) {
      return message.reply('❌ Please provide a song name or URL to play!');
    }

    const searchQuery = args.join(' ');
    
    try {
      // Get or create player
      const player = client.moon.create({
        guild: message.guild.id,
        voiceChannel: message.member.voice.channel.id,
        textChannel: message.channel.id,
        selfDeaf: true
      });

      // Connect to voice channel if not already connected
      if (player.state !== "CONNECTED") await player.connect();

      // Search for the track
      const result = await client.moon.search(searchQuery);
      
      if (!result || !result.tracks || result.tracks.length === 0) {
        return message.reply('❌ No results found for your query!');
      }

      // Add track(s) to queue
      const track = result.tracks[0];
      player.queue.add(track);
      
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription(`✅ Added **${track.title}** to the queue`)
          .setThumbnail(track.thumbnail || 'https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683d61f1&is=683c1071&hm=273c4845b5715d5b473f88b26b17c99192f9106d58542f86ba28c718012b4641&=&format=webp&quality=lossless&width=461&height=461')
          .setColor(emco);
        message.reply({ embeds: [embed] });
      } else {
        message.reply(`✅ Added **${track.title}** to the queue`);
      }

      // Play track if not already playing
      if (!player.playing && !player.paused) {
        await player.play();
      }
    } catch (error) {
      console.error('Error playing track:', error);
      message.reply('❌ An error occurred while trying to play the track.');
    }
  },

  // Skip to next song
  async skip(client, message) {
    const player = client.moon.players.get(message.guild.id);
    
    if (!player || !player.playing) {
      return message.reply('🎶 There must be music playing to use that!');
    }
    
    try {
      const currentTrack = player.queue.current;
      await player.skip();
      
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription(`***ϟ Skipped ${currentTrack ? currentTrack.title : 'current song'}***`)
          .setColor(emco)
          .setThumbnail("https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683d61f1&is=683c1071&hm=273c4845b5715d5b473f88b26b17c99192f9106d58542f86ba28c718012b4641&=&format=webp&quality=lossless&width=461&height=461");
        message.channel.send({ embeds: [embed] });
      } else {
        message.channel.send(`_skipped, the next song is now playing_`);
      }
    } catch (error) {
      console.error('Error skipping track:', error);
      message.reply('❌ An error occurred while trying to skip the track.');
    }
  },

  // Stop playing and clear queue
  async stop(client, message) {
    const player = client.moon.players.get(message.guild.id);
    
    if (!player) {
      return message.reply('🎶 There must be music playing to use that!');
    }
    
    try {
      player.destroy();
      
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription('**✅ Music playback stopped and queue cleared.**')
          .setColor(emco);
        message.reply({ embeds: [embed] });
      } else {
        message.reply('✅ Music playback stopped and queue cleared.');
      }
    } catch (error) {
      console.error('Error stopping playback:', error);
      message.reply('❌ An error occurred while trying to stop the music.');
    }
  },

  // Display current queue
  async queue(client, message) {
    const player = client.moon.players.get(message.guild.id);
    
    if (!player || !player.queue.length) {
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription(`**🎶 There must be music playing to use that!**`)
          .setThumbnail("https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683d61f1&is=683c1071&hm=273c4845b5715d5b473f88b26b17c99192f9106d58542f86ba28c718012b4641&=&format=webp&quality=lossless&width=461&height=461")
          .setColor(emco);
        return message.reply({ embeds: [embed] });
      } else {
        return message.reply(`🎶 There must be music playing to use that!`);
      }
    }
    
    const currentTrack = player.queue.current;
    const queueTracks = player.queue;
    
    let songNames = [];
    if (currentTrack) {
      songNames.push(`*Now playing:* \n\`1\`. ${currentTrack.title}`);
    }

    if (queueTracks.length > 0) {
      songNames = songNames.concat(
        queueTracks.map((track, index) => `\`${index + 2}\`. ${track.title}`)
      );
    }
    
    const songsDescription = songNames.join('\n');
    
    if (useEmbeds) {
      const embed = new MessageEmbed()
        .setAuthor({ name: `ϟ Total songs: (${queueTracks.length + (currentTrack ? 1 : 0)})` })
        .setDescription(songsDescription)
        .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205557078890905610/ddddd.png")
        .setColor(emco)
        .setFooter({ text: `${client.user.username}`, iconURL: client.user.displayAvatarURL({ dynamic: true }) });
      message.channel.send({ embeds: [embed] });
    } else {
      message.channel.send(`**Queue (${queueTracks.length + (currentTrack ? 1 : 0)} tracks):**\n${songsDescription}`);
    }
  },

  // Pause current playback
  async pause(client, message) {
    const player = client.moon.players.get(message.guild.id);
    
    if (!player || !player.playing) {
      return message.reply('🎶 There must be music playing to use that!');
    }
    
    try {
      await player.pause(true);
      
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription('**⏸️ Music playback paused.**')
          .setColor(emco);
        message.reply({ embeds: [embed] });
      } else {
        message.reply('⏸️ Music playback paused.');
      }
    } catch (error) {
      console.error('Error pausing playback:', error);
      message.reply('❌ An error occurred while trying to pause the music.');
    }
  },

  // Resume paused playback
  async resume(client, message) {
    const player = client.moon.players.get(message.guild.id);
    
    if (!player) {
      return message.reply('🎶 There must be music playing to use that!');
    }
    
    try {
      await player.pause(false);
      
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription('**▶️ Music playback resumed.**')
          .setColor(emco);
        message.reply({ embeds: [embed] });
      } else {
        message.reply('▶️ Music playback resumed.');
      }
    } catch (error) {
      console.error('Error resuming playback:', error);
      message.reply('❌ An error occurred while trying to resume the music.');
    }
  },

  // Change volume
  async volume(client, message, args) {
    const player = client.moon.players.get(message.guild.id);
    
    if (!player) {
      return message.reply('🎶 There must be music playing to use that!');
    }
    
    if (!args[0] || isNaN(args[0]) || Number(args[0]) < 0 || Number(args[0]) > 100) {
      return message.reply('❌ Please provide a volume level between 0 and 100.');
    }
    
    const volume = Number(args[0]);
    
    try {
      player.setVolume(volume);
      
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription(`**🔊 Volume set to ${volume}%.**`)
          .setColor(emco);
        message.reply({ embeds: [embed] });
      } else {
        message.reply(`*ϟ Volume changed to **\`${volume}%\`** .*`);
      }
    } catch (error) {
      console.error('Error changing volume:', error);
      message.reply('❌ An error occurred while trying to change the volume.');
    }
  }
};
