import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
export declare type APIApplicationCommandBooleanOption = APIApplicationCommandOptionBase<ApplicationCommandOptionType.Boolean>;
export declare type APIApplicationCommandInteractionDataBooleanOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.Boolean, boolean>;
//# sourceMappingURL=boolean.d.ts.map