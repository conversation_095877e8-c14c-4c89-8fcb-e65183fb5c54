{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EACX,qBAAqB,EACrB,kBAAkB,EAClB,aAAa,EACb,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,4BAA4B,EAC5B,mBAAmB,EACnB,aAAa,EACb,eAAe,EACf,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,yBAAyB,EACzB,gBAAgB,EAChB,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,EAAE,oDAAoD,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEjH,MAAM,WAAW,wBAAyB,SAAQ,mCAAmC;IACpF,EAAE,EAAE,SAAS,CAAC;CACd;AAED;;GAEG;AACH,oBAAY,uBAAuB,GAAG,UAAU,CAAC;AAEjD;;GAEG;AACH,oBAAY,2BAA2B,GAAG,oDAAoD,CAAC;IAC9F;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;;;OAKG;IACH,IAAI,CAAC,EAAE,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IACrD;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACtB;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,wBAAwB,EAAE,GAAG,IAAI,CAAC;IAC1D;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;IAC7B;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAC7C;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,yBAAyB,CAAC;IAClD;;;;OAIG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;;;OAIG;IACH,6BAA6B,CAAC,EAAE,yBAAyB,CAAC;IAC1D;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;CACpB,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,yBAAyB,GAAG,UAAU,CAAC;AAEnD;;GAEG;AACH,oBAAY,0BAA0B,GAAG,UAAU,CAAC;AAEpD;;GAEG;AACH,MAAM,WAAW,8BAA8B;IAC9C;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,+BAA+B,GAAG,UAAU,EAAE,CAAC;AAE3D;;GAEG;AACH,oBAAY,8BAA8B,GAAG,UAAU,CAAC;AAExD;;GAEG;AACH,oBAAY,uBAAuB,GAAG,aAAa,CAAC,mBAAmB,CAAC,GACvE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC,GACjD,oDAAoD,CAAC;IACpD;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC7B,CAAC,CAAC;AAEJ;;GAEG;AACH,oBAAY,iCAAiC,GAAG,oDAAoD,CAAC;IACpG;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IACd;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC;IACpB;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,CAAC;IACtC;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,uBAAuB,CAAC;IAC5C;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,CAAC;IACnE;;;;OAIG;IACH,WAAW,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACvF;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,GAAG,aAAa,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACvG;;OAEG;IACH,KAAK,CAAC,EAAE,YAAY,CAAC;CACrB,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,qCAAqC,GAC9C,CAAC;IACD;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACrB,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,GACxC,CAAC,iCAAiC,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAE7E;;GAEG;AACH,oBAAY,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,oBAAY,wCAAwC,GAAG,UAAU,CAAC;AAElE;;GAEG;AACH,oBAAY,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;GAEG;AACH,oBAAY,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;GAEG;AACH,oBAAY,6CAA6C,GAAG,KAAK,CAAC;AAKlE,MAAM,WAAW,0CAA0C;IAC1D;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,2CAA2C,GAAG,OAAO,EAAE,CAAC;AAEpE;;GAEG;AACH,oBAAY,6CAA6C,GAAG,KAAK,CAAC;AAElE;;GAEG;AACH,oBAAY,yCAAyC,GAAG,KAAK,CAAC;AAE9D;;GAEG;AACH,oBAAY,kCAAkC,GAAG,oDAAoD,CAAC;IACrG;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAC3B;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC;IAC5B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC;IAC7C;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;IACvG;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,GAAG,IAAI,CAAC;CAC1E,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,sCAAsC,GAC/C,CAAC;IACD;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACrB,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,GACxC,CAAC,kCAAkC,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAE9E;;GAEG;AACH,oBAAY,gCAAgC,GAAG,UAAU,CAAC;AAE1D;;GAEG;AACH,oBAAY,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,4CAA4C;IAC5D;;OAEG;IACH,QAAQ,EAAE,SAAS,EAAE,CAAC;CACtB;AAED;;GAEG;AACH,oBAAY,0CAA0C,GAAG,KAAK,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;IAC3B;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;IAC1B;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;CACpB;AAED;;GAEG;AACH,oBAAY,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;GAEG;AACH,oBAAY,8BAA8B,GAAG,iBAAiB,EAAE,CAAC;AAEjE;;GAEG;AACH,oBAAY,gCAAgC,GAAG,oDAAoD,CAAC;IACnG;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;;;OAIG;IACH,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAC/B;;;;OAIG;IACH,cAAc,CAAC,EAAE,SAAS,CAAC;IAC3B;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,SAAS,CAAC;CAClC,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,8BAA8B,GAAG,iBAAiB,CAAC;AAE/D;;GAEG;AACH,oBAAY,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD;;OAEG;IACH,kBAAkB,EAAE,SAAS,CAAC;CAC9B;AAED;;GAEG;AACH,oBAAY,iCAAiC,GAAG,kBAAkB,CAAC;AAEnE;;GAEG;AACH,oBAAY,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;GAEG;AACH,oBAAY,2BAA2B,GAAG,UAAU,EAAE,CAAC;AAEvD;;GAEG;AACH,oBAAY,0BAA0B,GAAG,KAAK,CAAC;AAE/C;;GAEG;AACH,oBAAY,6BAA6B,GAAG,KAAK,CAAC;AAElD;;GAEG;AACH,oBAAY,kCAAkC,GAAG,oDAAoD,CAAC;IACrG;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACd,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,gCAAgC,GAAG,OAAO,CAAC;AAEvD;;GAEG;AACH,oBAAY,mCAAmC,GAAG,OAAO,CAAC;AAE1D;;GAEG;AACH,oBAAY,yCAAyC,GAAG,oDAAoD,CAAC;IAC5G;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,qBAAqB,EAAE,yBAAyB,CAAC;IACjD;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAC7B,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,oCAAoC,GAAG,yCAAyC,GAAG;IAC9F;;OAEG;IACH,OAAO,EAAE,iCAAiC,CAAC;CAC3C,CAAC;AAEF;;GAEG;AACH,oBAAY,wCAAwC,GAAG,yCAAyC,GAAG;IAClG;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF;;GAEG;AACH,oBAAY,uCAAuC,GAAG,UAAU,CAAC;AAEjE;;GAEG;AACH,oBAAY,iCAAiC,GAAG,yCAAyC,GACxF,oDAAoD,CAAC;IACpD;;;;;;;;;OASG;IACH,IAAI,CAAC,EAAE,WAAW,CAAC,eAAe,GAAG,WAAW,CAAC,iBAAiB,GAAG,WAAW,CAAC,kBAAkB,CAAC;IACpG;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;CACpB,CAAC,CAAC;AAEJ;;GAEG;AACH,oBAAY,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,oBAAY,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;GAEG;AACH,oBAAY,uCAAuC,GAAG,KAAK,CAAC;AAE5D;;GAEG;AACH,oBAAY,oCAAoC,GAAG,eAAe,EAAE,CAAC;AAKrE,MAAM,WAAW,qCAAqC;IACrD;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC5B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,2CAA2C,GAAG,aAAa,CAAC"}