{"version": 3, "file": "interactions.d.ts", "sourceRoot": "", "sources": ["interactions.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACX,0CAA0C,EAC1C,uCAAuC,EACvC,+CAA+C,EAC/C,2CAA2C,EAC3C,yCAAyC,EACzC,qCAAqC,EACrC,MAAM,WAAW,CAAC;AACnB,OAAO,KAAK,EACX,qBAAqB,EACrB,+BAA+B,EAC/B,qCAAqC,EACrC,sBAAsB,EACtB,kCAAkC,EAClC,sBAAsB,EACtB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,oDAAoD,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEjH;;;GAGG;AACH,oBAAY,mCAAmC,GAAG,qBAAqB,EAAE,CAAC;AAE1E;;;GAGG;AACH,oBAAY,kCAAkC,GAAG,qBAAqB,CAAC;AAEvE,aAAK,0CAA0C,GAAG,oDAAoD,CACrG,IAAI,CAAC,qBAAqB,EAAE,IAAI,GAAG,gBAAgB,GAAG,aAAa,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC,CACtG,CAAC;AAEF;;;GAGG;AACH,oBAAY,+CAA+C,GAAG,0CAA0C,GACvG,oDAAoD,CAAC;IACpD,IAAI,CAAC,EAAE,sBAAsB,CAAC,SAAS,CAAC;IACxC,WAAW,EAAE,MAAM,CAAC;CACpB,CAAC,CAAC;AAEJ;;;GAGG;AACH,MAAM,WAAW,iDAAkD,SAAQ,0CAA0C;IACpH,IAAI,EAAE,sBAAsB,CAAC,IAAI,GAAG,sBAAsB,CAAC,OAAO,CAAC;CACnE;AAED;;;GAGG;AACH,oBAAY,sCAAsC,GAC/C,+CAA+C,GAC/C,iDAAiD,CAAC;AAErD;;;GAGG;AACH,oBAAY,oCAAoC,GAAG,qBAAqB,CAAC;AAEzE;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,aAAa,CAAC,sCAAsC,CAAC,CAAC;AAE3G;;;GAGG;AACH,oBAAY,oCAAoC,GAAG,qBAAqB,CAAC;AAEzE;;;GAGG;AACH,oBAAY,qCAAqC,GAAG,sCAAsC,EAAE,CAAC;AAE7F;;;GAGG;AACH,oBAAY,mCAAmC,GAAG,qBAAqB,EAAE,CAAC;AAE1E;;;GAGG;AACH,oBAAY,wCAAwC,GAAG,qBAAqB,EAAE,CAAC;AAE/E;;;GAGG;AACH,oBAAY,uCAAuC,GAAG,qBAAqB,CAAC;AAE5E;;;GAGG;AACH,oBAAY,2CAA2C,GAAG,sCAAsC,CAAC;AAEjG;;;GAGG;AACH,oBAAY,yCAAyC,GAAG,qBAAqB,CAAC;AAE9E;;;GAGG;AACH,oBAAY,2CAA2C,GAAG,aAAa,CAAC,sCAAsC,CAAC,CAAC;AAEhH;;;GAGG;AACH,oBAAY,yCAAyC,GAAG,qBAAqB,CAAC;AAE9E;;;GAGG;AACH,oBAAY,0CAA0C,GAAG,sCAAsC,EAAE,CAAC;AAElG;;;GAGG;AACH,oBAAY,wCAAwC,GAAG,qBAAqB,EAAE,CAAC;AAE/E;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,sBAAsB,CAAC;AAE5E;;;GAGG;AACH,oBAAY,0CAA0C,GACnD,CAAC;IACD;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACrB,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,GACxC,CAAC,sCAAsC,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAElF;;;GAGG;AACH,oBAAY,2CAA2C,GAAG,uCAAuC,CAAC;AAElG;;;GAGG;AACH,oBAAY,+CAA+C,GAAG,2CAA2C,CAAC;AAE1G;;;GAGG;AACH,oBAAY,mDAAmD,GAAG,+CAA+C,CAAC;AAElH;;;GAGG;AACH,oBAAY,6CAA6C,GAAG,yCAAyC,CAAC;AAEtG;;;GAGG;AACH,oBAAY,8CAA8C,GAAG,0CAA0C,CAAC;AAExG;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,kCAAkC,CAAC;AAExF;;;GAGG;AACH,oBAAY,0CAA0C,GACnD,CAAC;IACD;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACrB,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,GACxC,CAAC,sCAAsC,GAAG,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAElF;;;GAGG;AACH,oBAAY,oCAAoC,GAAG,qCAAqC,CAAC;AAEzF;;;GAGG;AACH,oBAAY,mCAAmC,GAAG,uCAAuC,CAAC;AAE1F;;;GAGG;AACH,oBAAY,uCAAuC,GAAG,2CAA2C,CAAC;AAElG;;;GAGG;AACH,oBAAY,2CAA2C,GAAG,+CAA+C,CAAC;AAE1G;;;GAGG;AACH,oBAAY,qCAAqC,GAAG,yCAAyC,CAAC;AAE9F;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,0CAA0C,CAAC;AAEhG;;;GAGG;AACH,oBAAY,mDAAmD,GAAG,qCAAqC,EAAE,CAAC;AAE1G;;;GAGG;AACH,oBAAY,6CAA6C,GAAG,qCAAqC,CAAC;AAElG;;;GAGG;AACH,MAAM,WAAW,+CAA+C;IAC/D,WAAW,EAAE,+BAA+B,EAAE,CAAC;CAC/C;AAED;;;GAGG;AACH,oBAAY,6CAA6C,GAAG,qCAAqC,CAAC;AAElG;;;GAGG;AACH,oBAAY,qDAAqD,GAAG,IAAI,CACvE,qCAAqC,EACrC,IAAI,GAAG,aAAa,CACpB,EAAE,CAAC;AAEJ;;;GAGG;AACH,oBAAY,mDAAmD,GAAG,qCAAqC,EAAE,CAAC"}