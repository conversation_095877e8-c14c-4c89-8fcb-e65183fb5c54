{"version": 3, "file": "Manager.js", "sourceRoot": "", "sources": ["../../../src/core/Manager.ts"], "names": [], "mappings": ";;;AAAA,6CAA2C;AAS3C,uCAWqB;AASrB,MAAa,OAAQ,SAAQ,0BAAY;IAChC,UAAU,GAAY,KAAK,CAAC;IACnB,OAAO,CAAkB;IACzB,WAAW,CAAW;IAC/B,KAAK,CAAc;IACnB,OAAO,GAAkB,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACpE,OAAO,GAAW,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;IACjD,QAAQ,CAAW;IACnB,OAAO,CAAgB;IAC9B,YAAY,MAAsB;QAChC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,MAAM,EAAE,WAAW,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG;YACb,UAAU,EAAE,eAAe,IAAI,CAAC,OAAO,4CAA4C;YACnF,qBAAqB,EAAE,SAAS;YAChC,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,KAAK;YACtB,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE;YACxC,sBAAsB,EAAE,KAAK;YAC7B,mBAAmB,EAAE,KAAK;YAC1B,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,KAAK;YACjB,eAAe,EAAE,KAAK;YACtB,GAAG,MAAM,CAAC,OAAO;SAClB,CAAC;QACF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAEpE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACpC,IAAI,MAAM,CAAC,UAAU,IAAI,IAAA,uBAAe,EAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC9E,MAAM,IAAI,KAAK,CACb,wBAAwB,MAAM,CAAC,IAAI,IAAI,SAAS,8BAA8B,MAAM,CAAC,UAAU,sBAAsB,IAAI,CAAC,OAAO,EAAE,CACpI,CAAC;oBACJ,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IACM,IAAI,CAAC,QAAgB;QAC1B,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;YAC9B,IAAA,wBAAgB,EACd,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAC1B,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EACzD,iEAAiE,CAClE,CAAC;YACF,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAA,WAAG,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAClF,CAAC;QACD,iBAAS,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,0CAA0C,GAAG,QAAQ,GAAG,iBAAiB,CAAC,CAAC;QAC9F,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,6BAA6B,GAAG,CAAC,OAAO,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,UAAU,CAAC,GAAG,KAAK,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,aAAa,GAAG,CAAC,OAAO,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,UAAU,CAAC,GAAG,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7T,CAAC;IACM,KAAK,CAAC,MAAM,CAAC,OAKnB;QACC,OAAO,IAAI,OAAO,CAAC,KAAK,EAAC,OAAO,EAAC,EAAE;YACjC,IAAA,wBAAgB,EACd,OAAO,EACP,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,EAC5B,wDAAwD,CACzD,CAAC;YACF,IAAA,wBAAgB,EACd,OAAO,CAAC,KAAK,EACb,KAAK,CAAC,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,EAClC,sDAAsD,CACvD,CAAC;YAEF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAC5B,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;YACxE,MAAM,CAAE,OAAO,EAAE,aAAa,CAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC9E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,OAAO,EAAE,CAAC;gBAClD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC;gBACtD,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,IAAI,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACrD,OAAO,OAAO,CAAC,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAED,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB;gBAClC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAC5B,CAAC;gBACD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;gBACnD,MAAM,IAAI,GAAG,MAAM,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACvD,OAAO,OAAO,CAAC,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC1E,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC7D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAE;gBAC/B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAEpB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,OAAO,CAAC,IAAI,CAAC,iBAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,MAAW;QACnC,IAAI,CAAC,CAAC,oBAAoB,EAAE,qBAAqB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YAAE,OAAO;QAE9E,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU;YAAE,OAAO;QAEpD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,CAAC,MAAM,CAAC,UAAU;YAAE,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;QAE/C,IAAI,MAAM,CAAC,CAAC,KAAK,qBAAqB,EAAE,CAAC;YACvC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;YAE/C,IAAI,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACtB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBAC3E,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBACxB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;oBACvB,IAAI,CAAC,IAAI,CACP,OAAO,EACP,iCAAiC,MAAM,eAAe,MAAM,CAAC,OAAO,EAAE,CACvE,CAAC;oBACF,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC9E,IAAI,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAC9B,CAAC;wBACF,IAAI,OAAO,EAAE,CAAC;4BACZ,IAAI,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAC9B,CAAC;4BAEF,IAAI,CAAC,IAAI,CACP,OAAO,EACP,mCAAmC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,OAAO,CAAC,IAAI,EAAE,CACzE,CAAC;4BAEF,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;wBACxB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,wDAAwD,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7F,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,MAAM,CAAC,CAAC,KAAK,oBAAoB,EAAE,CAAC;YAC7C,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAAE,OAAO;YAEvD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBACzB,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;gBACzB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACvB,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC7B,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;gBAEvB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,2CAA2C,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;gBACjF,OAAO;YACT,CAAC;YAED,IAAI,MAAM,CAAC,CAAC,CAAC,UAAU,KAAK,MAAM,CAAC,cAAc,EAAE,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAC7E,IAAI,CAAC,IAAI,CACP,OAAO,EACP,kCAAkC,MAAM,CAAC,CAAC,CAAC,UAAU,aAAa,MAAM,CAAC,OAAO,EAAE,CACnF,CAAC;gBACF,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;YAC9C,CAAC;YAED,MAAM,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;YAElD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,uDAAuD,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5F,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,UAAU,GAAgB,MAAM,CAAC,UAAU,CAAC;QAElD,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACvE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,qDAAqD,OAAO,WAAW,CAAC,CAAC;YAC5F,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,GAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAChD,OAAO;YACP,IAAI,EAAE;gBACJ,KAAK,EAAE;oBACL,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,QAAQ,EAAE,UAAU,CAAC,QAAQ;iBAC9B;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CACP,OAAO,EACP,0CACE,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,IACxC,cAAc,OAAO,EAAE,CACxB,CAAC;QAEF,IAAI,QAAQ;YAAE,MAAM,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,YAAY,CAAC,MAAqB;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAIM,SAAS,CAAC,OAAe;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAIM,SAAS,CAAC,OAAe;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAIM,YAAY,CAAC,OAAe;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5B,CAAC;CACF;AA1PD,0BA0PC"}