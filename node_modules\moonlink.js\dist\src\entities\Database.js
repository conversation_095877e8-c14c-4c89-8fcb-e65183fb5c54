"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Database = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const index_1 = require("../../index");
class Database {
    disabled = false;
    data = {};
    id;
    constructor(manager) {
        this.id = manager.options.clientId;
        this.disabled = manager.options.disableDatabase && !manager.options.resume;
        if (this.disabled) {
            this.data = {};
            index_1.Structure.getManager().emit("debug", `Moonlink.js > Database > Database is disabled, no data will be loaded/saved`);
            return;
        }
        else {
            index_1.Structure.getManager().emit("debug", `Moonlink.js > Database > Database is enabled, loading data...`);
        }
        this.loadData();
    }
    set(key, value) {
        if (this.disabled)
            return;
        if (!key)
            throw new Error("Key cannot be empty");
        this.modifyData(key, value);
        this.saveData();
    }
    get(key) {
        if (this.disabled)
            return undefined;
        if (!key)
            throw new Error("Key cannot be empty");
        return key.split(".").reduce((acc, curr) => acc?.[curr], this.data) ?? undefined;
    }
    push(key, value) {
        if (this.disabled)
            return;
        const arr = this.get(key) || [];
        if (!Array.isArray(arr))
            throw new Error("Key does not point to an array");
        arr.push(value);
        this.set(key, arr);
    }
    delete(key) {
        if (this.disabled)
            return false;
        if (!key)
            throw new Error("Key cannot be empty");
        const keys = key.split(".");
        const lastKey = keys.pop();
        let current = this.data;
        for (const k of keys) {
            if (typeof current[k] !== "object")
                return false;
            current = current[k];
        }
        if (lastKey && lastKey in current) {
            delete current[lastKey];
            this.saveData();
            return true;
        }
        return false;
    }
    modifyData(key, value) {
        if (this.disabled)
            return;
        const keys = key.split(".");
        let current = this.data;
        keys.forEach((k, i) => {
            if (i === keys.length - 1) {
                current[k] = value;
            }
            else {
                current[k] = current[k] || {};
                current = current[k];
            }
        });
    }
    loadData() {
        if (this.disabled)
            return;
        const filePath = this.getFilePath();
        if (fs_1.default.existsSync(filePath)) {
            index_1.Structure.getManager().emit("debug", `Moonlink.js > Database > Loading data from ${filePath}`);
            try {
                const fileContent = fs_1.default.readFileSync(filePath, "utf-8");
                this.data = JSON.parse(fileContent);
            }
            catch (err) {
                index_1.Structure.getManager().emit("debug", `Moonlink.js > Database > Error loading/parsing data: ${err}`);
                this.data = {};
            }
        }
        else {
            index_1.Structure.getManager().emit("debug", `Moonlink.js > Database > No data found for clientId(${this.id})`);
        }
    }
    saveData() {
        if (this.disabled)
            return;
        try {
            const filePath = this.getFilePath();
            fs_1.default.mkdirSync(path_1.default.dirname(filePath), { recursive: true });
            fs_1.default.writeFileSync(filePath, JSON.stringify(this.data, null, 2));
        }
        catch (err) {
            index_1.Structure.getManager().emit("debug", `Moonlink.js > Database > Failed to save data: ${err}`);
        }
    }
    getFilePath() {
        return path_1.default.resolve(__dirname, "../datastore", `data.${this.id}.json`);
    }
}
exports.Database = Database;
//# sourceMappingURL=Database.js.map