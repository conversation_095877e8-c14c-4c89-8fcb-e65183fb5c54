const { owners, prefix } = require(`${process.cwd()}/config`);
const { Client, Intents, MessageEmbed } = require('discord.js');
const fs = require('fs');

module.exports = {
  name: 'bulktoken',
  run: async (client, message) => {

    if (!owners.includes(message.author.id)) return;

    if (message.author.bot) return;

    const botIntents = [
      Intents.FLAGS.GUILDS,
      Intents.FLAGS.GUILD_MESSAGES,
    ];

    // التحقق من وجود مرفق (ملف نصي)
    if (message.attachments.size > 0) {
      const attachment = message.attachments.first();
      
      // التحقق من أن الملف نصي
      if (!attachment.name.endsWith('.txt')) {
        return message.reply('**يرجى إرفاق ملف نصي (.txt) يحتوي على التوكنات.**');
      }

      try {
        // تحميل محتوى الملف
        const response = await fetch(attachment.url);
        const fileContent = await response.text();
        
        // تقسيم التوكنات (كل سطر توكن)
        const tokenLines = fileContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);
        
        if (tokenLines.length === 0) {
          return message.reply('**الملف فارغ أو لا يحتوي على توكنات صالحة.**');
        }

        await processTokens(message, tokenLines, botIntents);

      } catch (error) {
        console.error('خطأ في قراءة الملف:', error);
        return message.reply('**حدث خطأ أثناء قراءة الملف.**');
      }
    } else {
      // إذا لم يكن هناك مرفق، استخدم التوكنات من النص
      const args = message.content.split(' ');
      const command = args.shift().toLowerCase();
      const tokenValues = args;

      if (tokenValues.length === 0) {
        return message.reply('**يرجى إرفاق ملف نصي يحتوي على التوكنات أو كتابة التوكنات بعد الأمر.**\n**مثال:** `bulktoken token1 token2 token3`\n**أو إرفاق ملف .txt يحتوي على التوكنات (كل توكن في سطر منفصل)**');
      }

      await processTokens(message, tokenValues, botIntents);
    }
  }
}

async function processTokens(message, tokenValues, botIntents) {
  const validTokens = [];
  const invalidTokens = [];
  const duplicateTokens = [];
  
  // قراءة التوكنات الموجودة مسبقاً
  let existingBots = [];
  try {
    const data = fs.readFileSync('./bots.json', 'utf8');
    existingBots = JSON.parse(data);
    if (!Array.isArray(existingBots)) {
      existingBots = [];
    }
  } catch (error) {
    console.error('❌>', error);
  }

  // رسالة التقدم
  const progressMessage = await message.reply(`**🔄 جاري فحص ${tokenValues.length} توكن...**`);

  let processedCount = 0;
  const totalTokens = tokenValues.length;

  for (const tokenValue of tokenValues) {
    processedCount++;
    
    // تحديث رسالة التقدم كل 10 توكنات
    if (processedCount % 10 === 0 || processedCount === totalTokens) {
      await progressMessage.edit(`**🔄 جاري فحص التوكنات... (${processedCount}/${totalTokens})**`);
    }

    // التحقق من التوكن المكرر
    const tokenExists = existingBots.some(bot => bot.token === tokenValue);
    if (tokenExists) {
      duplicateTokens.push(tokenValue);
      continue;
    }

    try {
      const clientCheck = new Client({ intents: botIntents });
      await clientCheck.login(tokenValue);
      await clientCheck.destroy();
      validTokens.push(tokenValue);
    } catch (error) {
      invalidTokens.push({
        token: tokenValue,
        error: error.message
      });
    }
  }

  // إنشاء تقرير النتائج
  const embed = new MessageEmbed()
    .setTitle('📊 تقرير إضافة التوكنات المجمعة')
    .setColor('#00ff00')
    .addField('✅ توكنات صالحة', `${validTokens.length}`, true)
    .addField('❌ توكنات غير صالحة', `${invalidTokens.length}`, true)
    .addField('🔄 توكنات مكررة', `${duplicateTokens.length}`, true)
    .addField('📈 إجمالي المعالجة', `${tokenValues.length}`, true)
    .setTimestamp();

  if (validTokens.length > 0) {
    // إضافة التوكنات الصالحة إلى bots.json
    for (const tokenValue of validTokens) {
      existingBots.push({
        token: tokenValue,
        Server: null,
        channel: null,
        chat: null,
        status: null,
        client: null,
        useEmbeds: false
      });
    }
    
    fs.writeFileSync('./bots.json', JSON.stringify(existingBots, null, 2));
    
    embed.setDescription(`**تم إضافة ${validTokens.length} توكن بنجاح إلى المخزن! ✅**`);
    
    // تغيير أسماء وصور البوتات
    setTimeout(async () => {
      await updateBotsProfile(validTokens, botIntents, progressMessage);
    }, 2000);
    
  } else {
    embed.setDescription('**لم يتم إضافة أي توكن صالح.**');
    embed.setColor('#ff0000');
  }

  await progressMessage.edit({ content: null, embeds: [embed] });

  // إرسال تفاصيل التوكنات غير الصالحة إذا كانت أقل من 10
  if (invalidTokens.length > 0 && invalidTokens.length <= 10) {
    let errorDetails = '**التوكنات غير الصالحة:**\n';
    invalidTokens.forEach((item, index) => {
      errorDetails += `${index + 1}. \`${item.token.substring(0, 20)}...\` - ${item.error}\n`;
    });
    
    if (errorDetails.length < 2000) {
      await message.channel.send(errorDetails);
    }
  }
}

async function updateBotsProfile(validTokens, botIntents, progressMessage) {
  let updatedCount = 0;
  const totalBots = validTokens.length;
  
  for (const tokenValue of validTokens) {
    try {
      const botClient = new Client({ intents: botIntents });
      await botClient.login(tokenValue);

      const randomNumber = Math.floor(1000 + Math.random() * 9000);
      await botClient.user.setUsername(`OrynStore-${randomNumber}`);
      await botClient.user.setAvatar('https://i.ibb.co/8q7Yw9s/IMG-8719.jpg');

      await botClient.destroy();
      updatedCount++;
      
      // تحديث التقدم كل 5 بوتات
      if (updatedCount % 5 === 0 || updatedCount === totalBots) {
        await progressMessage.edit(`**✅ تم تحديث ملفات ${updatedCount}/${totalBots} بوت...**`);
      }
      
    } catch (avatarError) {
      console.error(`❌ خطأ في تحديث البوت:`, avatarError.message);
    }
    
    // تأخير بسيط لتجنب rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  await progressMessage.edit(`**🎉 تم الانتهاء من تحديث جميع البوتات (${updatedCount}/${totalBots})!**`);
}
