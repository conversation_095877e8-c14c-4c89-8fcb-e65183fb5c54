import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
import type { Snowflake } from '../../../../../globals';
export declare type APIApplicationCommandUserOption = APIApplicationCommandOptionBase<ApplicationCommandOptionType.User>;
export declare type APIApplicationCommandInteractionDataUserOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.User, Snowflake>;
//# sourceMappingURL=user.d.ts.map