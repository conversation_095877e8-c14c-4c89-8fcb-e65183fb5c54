const client = require("../index");
const config = require('../config.json');

client.login(config.Token);

client.on("ready", async () => {
    console.log(`👑 Bot > ( ${client.user.tag} )`)
  });

client.on("messageCreate", async (message) => {
    if (
        message.author.bot ||
        !message.guild ||
        !message.content.toLowerCase().startsWith(client.config.prefix)
    )
        return;

    const [cmd, ...args] = message.content
        .slice(client.config.prefix.length)
        .trim()
        .split(/ +/g);

    const command = client.commands.get(cmd.toLowerCase()) || client.commands.find(c => c.aliases?.includes(cmd.toLowerCase()));

    if (!command) return;
    try {
        await command.run(client, message, args);
    } catch (error) {
        console.error(`Error executing command ${cmd}:`, error);
        message.reply('حدث خطأ أثناء تنفيذ الأمر. يرجى المحاولة مرة أخرى لاحقاً.').catch(() => {});
    }

});
