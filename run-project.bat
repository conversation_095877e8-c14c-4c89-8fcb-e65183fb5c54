@echo off
chcp 65001 >nul
echo ================================
echo    تشغيل مشروع البوت الموسيقي
echo    Running Music Bot Project
echo ================================
echo.

REM Check if Node.js is installed
echo فحص Node.js...
echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo ❌ Node.js is not installed!
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo Please install Node.js from: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متوفر
echo ✅ Node.js available

REM Check if Java is installed
echo فحص Java...
echo Checking Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت!
    echo ❌ Java is not installed!
    echo يرجى تثبيت Java 17+ من: https://adoptium.net
    echo Please install Java 17+ from: https://adoptium.net
    pause
    exit /b 1
)
echo ✅ Java متوفر
echo ✅ Java available

REM Check if Lavalink.jar exists
echo فحص Lavalink...
echo Checking Lavalink...
if not exist "Lavalink.jar" (
    echo ❌ Lavalink.jar غير موجود!
    echo ❌ Lavalink.jar not found!
    echo.
    echo هل تريد تحميله الآن؟ (y/n)
    echo Do you want to download it now? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        call download-lavalink.bat
        if not exist "Lavalink.jar" (
            echo فشل في تحميل Lavalink
            echo Failed to download Lavalink
            pause
            exit /b 1
        )
    ) else (
        echo يرجى تحميل Lavalink.jar يدوياً
        echo Please download Lavalink.jar manually
        pause
        exit /b 1
    )
)
echo ✅ Lavalink متوفر
echo ✅ Lavalink available

REM Install dependencies
echo تثبيت المتطلبات...
echo Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ تم تثبيت المتطلبات
echo ✅ Dependencies installed

echo.
echo ================================
echo بدء تشغيل الخوادم...
echo Starting servers...
echo ================================

REM Start Lavalink in background
echo تشغيل Lavalink...
echo Starting Lavalink...
start "Lavalink Server" cmd /c "java -jar Lavalink.jar"

REM Wait for Lavalink to start
echo انتظار تشغيل Lavalink (10 ثوان)...
echo Waiting for Lavalink to start (10 seconds)...
timeout /t 10 /nobreak >nul

REM Start the bot
echo تشغيل البوت...
echo Starting bot...
node index.js

pause
