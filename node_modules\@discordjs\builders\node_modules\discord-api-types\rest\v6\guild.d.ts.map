{"version": 3, "file": "guild.d.ts", "sourceRoot": "", "sources": ["guild.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,oCAAoC,EAAE,MAAM,WAAW,CAAC;AACtE,OAAO,KAAK,EACX,MAAM,EACN,UAAU,EACV,QAAQ,EACR,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,sBAAsB,EACtB,SAAS,EACT,OAAO,EACP,cAAc,EACd,gCAAgC,EAChC,0BAA0B,EAC1B,YAAY,EACZ,sBAAsB,EACtB,gBAAgB,EAChB,yBAAyB,EACzB,MAAM,yBAAyB,CAAC;AAEjC;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,oCAAoC;IACpF,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,oBAAY,4BAA4B,GAAG,OAAO,CACjD,IAAI,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,SAAS,GAAG,YAAY,GAAG,qBAAqB,CAAC,CAC9F,GAAG;IACH,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAC5B,qBAAqB,CAAC,EAAE,uBAAuB,EAAE,CAAC;CAClD,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,4BAA4B;IACvE,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;CACpB;AAED;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACzC,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,kBAAkB,CAAC,EAAE,sBAAsB,CAAC;IAC5C,6BAA6B,CAAC,EAAE,gCAAgC,CAAC;IACjE,uBAAuB,CAAC,EAAE,0BAA0B,CAAC;IACrD,KAAK,CAAC,EAAE,kBAAkB,EAAE,CAAC;IAC7B,QAAQ,CAAC,EAAE,4BAA4B,EAAE,CAAC;IAC1C,cAAc,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,iBAAiB,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;CACpC;AAED;;GAEG;AACH,oBAAY,uBAAuB,GAAG,QAAQ,CAAC;AAE/C;;;GAGG;AACH,MAAM,WAAW,oBAAoB;IACpC,WAAW,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,oBAAY,qBAAqB,GAAG,QAAQ,CAAC;AAE7C;;;GAGG;AACH,oBAAY,4BAA4B,GAAG,eAAe,CAAC;AAE3D;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACzC,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,kBAAkB,CAAC,EAAE,sBAAsB,CAAC;IAC5C,6BAA6B,CAAC,EAAE,gCAAgC,CAAC;IACjE,uBAAuB,CAAC,EAAE,0BAA0B,CAAC;IACrD,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC,yBAAyB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1C,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC;IAC1B,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC5B;AAED;;GAEG;AACH,oBAAY,uBAAuB,GAAG,QAAQ,CAAC;AAE/C;;;GAGG;AACH,oBAAY,wBAAwB,GAAG,KAAK,CAAC;AAE7C;;;GAGG;AACH,oBAAY,6BAA6B,GAAG,UAAU,EAAE,CAAC;AAEzD;;;GAGG;AACH,oBAAY,+BAA+B,GAAG,OAAO,CACpD,IAAI,CACH,UAAU,EACV,MAAM,GAAG,uBAAuB,GAAG,OAAO,GAAG,MAAM,GAAG,SAAS,GAAG,YAAY,GAAG,qBAAqB,GAAG,WAAW,CACpH,CACD,GACA,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;AAEpC;;GAEG;AACH,oBAAY,6BAA6B,GAAG,UAAU,CAAC;AAEvD;;;GAGG;AACH,oBAAY,yCAAyC,GAAG,KAAK,CAAC;IAC7D,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,MAAM,CAAC;IACjB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1B,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,uCAAuC,GAAG,KAAK,CAAC;AAE5D;;;GAGG;AACH,oBAAY,2BAA2B,GAAG,cAAc,CAAC;AAEzD;;;GAGG;AACH,MAAM,WAAW,2BAA2B;IAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,4BAA4B,GAAG,cAAc,EAAE,CAAC;AAE5D;;GAEG;AACH,MAAM,WAAW,iCAAiC;IACjD,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,kCAAkC,GAAG,cAAc,EAAE,CAAC;AAElE;;;GAGG;AACH,MAAM,WAAW,6BAA6B;IAC7C,YAAY,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;IACjB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,IAAI,CAAC,EAAE,OAAO,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,2BAA2B,GAAG,cAAc,GAAG,SAAS,CAAC;AAErE;;;GAGG;AACH,MAAM,WAAW,+BAA+B;IAC/C,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACxB,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACtB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC3B;AAED;;GAEG;AACH,oBAAY,6BAA6B,GAAG,KAAK,CAAC;AAElD;;;GAGG;AACH,MAAM,WAAW,8CAA8C;IAC9D,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACrB;AAED;;GAEG;AACH,oBAAY,4CAA4C,GAAG,QAAQ,CAAC,8CAA8C,CAAC,CAAC;AAEpH;;;GAGG;AACH,oBAAY,+BAA+B,GAAG,KAAK,CAAC;AAEpD;;;GAGG;AACH,oBAAY,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;;GAGG;AACH,oBAAY,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;GAGG;AACH,oBAAY,yBAAyB,GAAG,MAAM,EAAE,CAAC;AAEjD;;;GAGG;AACH,oBAAY,wBAAwB,GAAG,MAAM,CAAC;AAE9C;;;GAGG;AACH,MAAM,WAAW,0BAA0B;IAC1C,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,MAAM,CAAC,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,oBAAY,wBAAwB,GAAG,KAAK,CAAC;AAE7C;;;GAGG;AACH,oBAAY,2BAA2B,GAAG,KAAK,CAAC;AAEhD;;;GAGG;AACH,oBAAY,0BAA0B,GAAG,OAAO,EAAE,CAAC;AAEnD;;;GAGG;AACH,MAAM,WAAW,4BAA4B;IAC5C,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IACrC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACvB,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;CAC7B;AAED;;GAEG;AACH,oBAAY,0BAA0B,GAAG,OAAO,CAAC;AAEjD;;;GAGG;AACH,oBAAY,sCAAsC,GAAG,KAAK,CAAC;IAC1D,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC,CAAC;AAEH;;GAEG;AACH,oBAAY,oCAAoC,GAAG,OAAO,EAAE,CAAC;AAE7D;;;GAGG;AACH,MAAM,WAAW,6BAA6B;IAC7C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAC9B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,WAAW,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,oBAAY,2BAA2B,GAAG,OAAO,CAAC;AAElD;;;GAGG;AACH,oBAAY,4BAA4B,GAAG,KAAK,CAAC;AAEjD;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAC9C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;;OAKG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACvB;AAED;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC/C,MAAM,EAAE,MAAM,CAAC;CACf;AAED;;;GAGG;AACH,MAAM,WAAW,6BAA6B;IAC7C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAED;;;GAGG;AACH,oBAAY,iCAAiC,GAAG,cAAc,EAAE,CAAC;AAEjE;;;GAGG;AACH,oBAAY,4BAA4B,GAAG,SAAS,EAAE,CAAC;AAEvD;;;GAGG;AACH,MAAM,WAAW,gCAAgC;IAChD,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED;;GAEG;AACH,oBAAY,iCAAiC,GAAG,mBAAmB,EAAE,CAAC;AAEtE;;;GAGG;AACH,MAAM,WAAW,mCAAmC;IACnD,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,oBAAY,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;GAGG;AACH,MAAM,WAAW,oCAAoC;IACpD,eAAe,CAAC,EAAE,yBAAyB,GAAG,IAAI,CAAC;IACnD,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,gBAAgB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;CAClC;AAED;;GAEG;AACH,oBAAY,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;;GAGG;AACH,oBAAY,mCAAmC,GAAG,KAAK,CAAC;AAExD;;;GAGG;AACH,oBAAY,qCAAqC,GAAG,KAAK,CAAC;AAE1D;;;GAGG;AACH,oBAAY,2BAA2B,GAAG,sBAAsB,CAAC;AAEjE;;;GAGG;AACH,oBAAY,mCAAmC,GAAG,sBAAsB,CAAC;AAEzE;;;GAGG;AACH,oBAAY,+BAA+B,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAE9E;;;GAGG;AACH,oBAAY,uCAAuC,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAEtF;;;GAGG;AACH,oBAAY,6BAA6B,GAAG,sBAAsB,CAAC;AAEnE;;GAEG;AACH,oBAAY,qCAAqC,GAAG,sBAAsB,CAAC;AAE3E;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAC9C,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;CACb;AAED;;;GAGG;AACH,MAAM,WAAW,+BAA+B;IAC/C,KAAK,CAAC,EAAE,gBAAgB,CAAC;CACzB;AAED;;;;GAIG;AACH,oBAAY,gCAAgC,GAAG,WAAW,CAAC"}