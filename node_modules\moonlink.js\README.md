<img src="/assets/moonlink_banner.png" alt="Moonlink.js - v4">
<div align="center">
<hr>
<img src="https://img.shields.io/badge/Made_with_♥️_in-Brazil-ED186A?style=for-the-badge"><br>
<a href="https://discord.gg/q8HzGuHuDY">
  <img src="https://img.shields.io/discord/990369410344701964?color=333&label=Support&logo=discord&style=for-the-badge" alt="Discord">
</a>
<a href="https://www.npmjs.com/package/moonlink.js">
  <img alt="NPM Downloads" src="https://img.shields.io/npm/dm/moonlink.js?style=for-the-badge&logo=npm&color=333">
</a>
<a href="https://www.npmjs.com/package/moonlink.js">
  <img alt="NPM Version" src="https://img.shields.io/npm/v/moonlink.js?style=for-the-badge&logo=npm&color=333">
</a>
<br>
<a href="https://github.com/Ecliptia/moonlink.js">
  <img alt="GitHub forks" src="https://img.shields.io/github/forks/Ecliptia/moonlink.js?style=for-the-badge&logo=github&color=333">
</a>
<a href="https://github.com/Ecliptia/moonlink.js">
  <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/Ecliptia/moonlink.js?style=for-the-badge&logo=github&color=333">
</a>
<a href="https://github.com/sponsors/1lucas1apk">
  <img alt="GitHub Sponsors" src="https://img.shields.io/github/sponsors/1lucas1apk?style=for-the-badge&logo=github&color=333">
</a>
<br>
Moonlink.js (Reimagined Version) - Envision a sonic adventure where imagination knows no bounds, wrapped in the magical spirit of festivities. 🌌 Moonlink.js unlocks your musical talent, offering an intuitive and straightforward experience.
</div>
<hr>

## Table of Contents

- [Documentation](#documentation)
- [License](#license)
- [Installation](#installation)
- [Support](#support)
- [Acknowledgements](#acknowledgements)

## Documentation

The full Moonlink.js documentation offers guides, tutorials, and detailed examples.  
- Learn how to create a music bot: [Creating a Music Bot](https://moonlink.js.org/getting-started/creating-a-music-bot)  
- Discover projects that use Moonlink.js: [Used By](https://moonlink.js.org/#used-by)

For more details, please visit the [Official Documentation](https://moonlink.js.org).

## License

This project is licensed under the [Open Software License ("OSL") v. 3.0](LICENSE) – see the [LICENSE](LICENSE) file for details.

## Installation

```bash
npm install moonlink.js
yarn add moonlink.js
pnpm install moonlink.js
bun install moonlink.js
```

## Support

Join our Discord server: [Moonlink.js - Imagine a Music Bot](https://discord.com/invite/xQq2A8vku3) for support, questions, and community discussions.

## Acknowledgements

Thank you to everyone who contributed to the development of Moonlink.js, whether by reporting bugs, installing the package, or helping the community.  
Have a great day! :)