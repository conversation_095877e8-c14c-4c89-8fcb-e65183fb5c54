const fs = require('fs');
const { owners, emco, logChannelId } = require(`${process.cwd()}/config`);
const { MessageEmbed } = require('discord.js');
const ms = require('ms');

module.exports = {
  name: 'addsubtime',
  run: async (client, message, args) => {
    // التحقق من صلاحيات المالك
    if (!owners.includes(message.author.id)) return;

    // التحقق من وجود الأرجومنتات المطلوبة
    const codeToAddTime = args[0];
    if (!codeToAddTime) {
      return message.channel.send(        new MessageEmbed()
          .setColor('#355a5d')
          .setDescription('❌ **يرجى إرفاق ايدي الاشتراك**')
      );
    }

    const timeToAdd = args[1];
    if (!timeToAdd || !ms(timeToAdd)) {
      return message.channel.send(
        new MessageEmbed()
          .setColor('#355a5d')
          .setDescription('❌ **يرجى إرفاق وقت صحيح**')
      );
    }    try {
      // التحقق من وجود الملف
      if (!fs.existsSync('./logs.json')) {
        fs.writeFileSync('./logs.json', '[]', 'utf8');
        console.log('تم إنشاء ملف logs.json جديد');
        return message.channel.send(
          new MessageEmbed()
            .setColor('#355a5d')
            .setDescription('❌ **تم إنشاء ملف السجلات. لا توجد أي اشتراكات مسجلة.**')
        );
      }
      
      // قراءة ملف السجلات
      const logs = fs.readFileSync('./logs.json', 'utf8');
      
      // تحليل محتوى الملف بشكل آمن
      let logsArray = [];
      try {
        if (logs.trim() === '') {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          return message.channel.send(
            new MessageEmbed()
              .setColor('#355a5d')
              .setDescription('❌ **لا توجد أي اشتراكات مسجلة.**')
          );
        }
        
        logsArray = JSON.parse(logs);
        if (!Array.isArray(logsArray)) {
          throw new Error('تنسيق البيانات غير صحيح');
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
        fs.writeFileSync('./logs.json', '[]', 'utf8');
        return message.channel.send(
          new MessageEmbed()
            .setColor('#355a5d')
            .setDescription('❌ **تم إصلاح ملف السجلات. لا توجد أي اشتراكات مسجلة.**')
        );
      }

      // البحث عن الاشتراك المطابق
      const matchingSubscription = logsArray.find(entry => entry.code === codeToAddTime);

      if (!matchingSubscription) {
        return message.channel.send(
          new MessageEmbed()
            .setColor('#355a5d')
            .setDescription('❌ **لا يوجد اشتراك مرتبط بهذا الايدي**')
        );
      }

      // تحديث وقت الانتهاء
      const newExpirationTime = matchingSubscription.expirationTime + ms(timeToAdd);
      matchingSubscription.expirationTime = newExpirationTime;

      // حفظ التغييرات
      fs.writeFileSync('./logs.json', JSON.stringify(logsArray, null, 2));

      // إرسال تأكيد للمستخدم
      message.react('✅');

      // إعداد بيانات السجل
      const adminName = message.author.username;
      const userId = matchingSubscription.user;
      const serverId = matchingSubscription.server;
      const botsCount = matchingSubscription.botsCount;
      const subscriptionTime = matchingSubscription.subscriptionTime;
      const expirationTime = matchingSubscription.expirationTime;
      const code = matchingSubscription.code;

      // إرسال سجل التعديل إلى قناة اللوج
      const logChannel = client.channels.cache.get(logChannelId);
      if (logChannel) {
        const logEmbed = new MessageEmbed()
          .setTitle('تمت إضافة وقت للاشتراك')
          .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410839650366/Oryn_9.png?ex=683d61f2&is=683c1072&hm=c4cbe47f27039b844f24d50c5c9923adca04d9ca195807f60761c4138b901315&=&format=webp&quality=lossless&width=864&height=486')
          .setDescription(`
            **المشرف:** <@${message.author.id}>
            **المستخدم:** <@${userId}>
            **الكود:** \`${code}\`
            **الوقت المضاف:** \`${timeToAdd}\`
          `)
          .setColor('#355a5d')
          .setTimestamp();

        await logChannel.send(logEmbed);
      }

      // إرسال تأكيد للمستخدم
      const successEmbed = new MessageEmbed()
        .setTitle('✅ تمت العملية بنجاح')
        .setDescription(`
          **تمت إضافة \`${timeToAdd}\` إلى الاشتراك:**
          - الكود: \`${codeToAddTime}\`
          - الوقت الجديد: <t:${Math.floor(newExpirationTime/1000)}:R>
        `)
        .setColor('#355a5d');

      await message.channel.send(successEmbed);    } catch (error) {
      console.error('❌> خطأ في أمر addsubtime:', error);
      
      // تحديد نوع الخطأ والتعامل معه بشكل مناسب
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        console.error('❌> خطأ في تحليل بيانات JSON:', error);
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          await message.channel.send(
            new MessageEmbed()
              .setColor('#355a5d')
              .setDescription('❌ **تم إصلاح ملف السجلات. الرجاء إعادة تنفيذ الأمر.**')
          );
        } catch (writeError) {
          console.error('❌> فشل في إصلاح ملف السجلات:', writeError);
          await message.channel.send(
            new MessageEmbed()
              .setColor('#355a5d')
              .setDescription('❌ **حدث خطأ أثناء محاولة إصلاح ملف السجلات. الرجاء الاتصال بمسؤول النظام.**')
          );
        }
      } else if (error.code === 'ENOENT') {
        try {
          fs.writeFileSync('./logs.json', '[]', 'utf8');
          await message.channel.send(
            new MessageEmbed()
              .setColor('#355a5d')
              .setDescription('❌ **تم إنشاء ملف السجلات. الرجاء إعادة تنفيذ الأمر.**')
          );
        } catch (writeError) {
          console.error('❌> فشل في إنشاء ملف السجلات:', writeError);
          await message.channel.send(
            new MessageEmbed()
              .setColor('#355a5d')
              .setDescription('❌ **حدث خطأ أثناء محاولة إنشاء ملف السجلات. الرجاء الاتصال بمسؤول النظام.**')
          );
        }
      } else {
        await message.channel.send(
          new MessageEmbed()
            .setColor('#355a5d')
            .setDescription('❌ **حدث خطأ أثناء معالجة الطلب. الرجاء المحاولة لاحقًا.**')
        );
      }
    }
  }
};