@echo off
echo تحميل Lavalink...
echo Downloading Lavalink...

REM Check if curl is available
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo curl غير متوفر. يرجى تحميل Lavalink يدوياً من:
    echo curl is not available. Please download Lavalink manually from:
    echo https://github.com/lavalink-devs/Lavalink/releases
    pause
    exit /b 1
)

REM Download latest Lavalink
echo جاري تحميل أحدث إصدار من Lavalink...
echo Downloading latest Lavalink release...

curl -L -o Lavalink.jar "https://github.com/lavalink-devs/Lavalink/releases/latest/download/Lavalink.jar"

if exist "Lavalink.jar" (
    echo تم تحميل Lavalink بنجاح!
    echo Lavalink downloaded successfully!
    echo.
    echo يمكنك الآن تشغيل start-lavalink.bat
    echo You can now run start-lavalink.bat
) else (
    echo فشل في تحميل Lavalink
    echo Failed to download Lavalink
    echo يرجى التحميل يدوياً من:
    echo Please download manually from:
    echo https://github.com/lavalink-devs/Lavalink/releases
)

pause
