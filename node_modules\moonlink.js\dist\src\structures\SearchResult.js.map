{"version": 3, "file": "SearchResult.js", "sourceRoot": "", "sources": ["../../../src/structures/SearchResult.ts"], "names": [], "mappings": ";;;AAAA,uCAAmD;AAUnD,MAAa,YAAY;IAChB,KAAK,CAAS;IACd,MAAM,CAAS;IACf,MAAM,CAAU;IAChB,QAAQ,CAAW;IACnB,YAAY,CAAgB;IAC5B,KAAK,CAAU;IAEtB,YAAY,GAAQ,EAAE,OAA4B;QAChD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAEO,aAAa,CAAC,GAAQ,EAAE,SAAkB;QAChD,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;YACxB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,SAAS,GAAU,EAAE,CAAC;QAC1B,QAAQ,GAAG,CAAC,QAAQ,EAAE,CAAC;YACrB,KAAK,OAAO,CAAC;YACb,KAAK,OAAO;gBACV,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACvB,MAAM;YACR,KAAK,QAAQ;gBACX,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC;gBACrB,MAAM;YACR,KAAK,UAAU;gBACb,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC5B,IAAI,CAAC,YAAY,GAAG;oBAChB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAC5B,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EACnC,CAAC,CACF;oBACH,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;oBACxB,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa;iBAC7C,CAAC;gBACF,MAAM;QACV,CAAC;QAED,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,aAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;CACF;AArDD,oCAqDC"}