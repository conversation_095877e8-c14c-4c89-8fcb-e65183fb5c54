const { Discord, Permissions, Intents, Client, MessageEmbed, MessageAttachment, Collection, Collector, MessageCollector, MessageActionRow, MessageButton, MessageSelectMenu, WebhookClient } = require('discord.js');
require('events').EventEmitter.defaultMaxListeners = 200;
const express = require('express');
const app = express();

app.get('/', (req, res) => {
  res.send('Hello Express app!')
});

try {
  const server = app.listen(30000, () => {
    console.log("Server Started..");
  });
  
  server.on('error', (e) => {
    if (e.code === 'EADDRINUSE') {
      console.log("Port 30000 is already in use. Server not started.");
    } else {
      console.error(e);
    }
  });
} catch (err) {
  console.log("Server could not be started:", err.message);
}

function convertTimeToSeconds(timeString) {
  const time = timeString.toLowerCase();
  const units = {
    s: 1,
    m: 60,
    h: 3600,
    d: 86400,
    w: 604800
  };

  const unit = time.charAt(time.length - 1);
  const value = parseInt(time.slice(0, time.length - 1));
  if (unit in units) {
    return value * units[unit];

  } else {
    return 0; 
  }
}


const client = new Client({
  intents: [
    Intents.FLAGS.GUILDS,
    Intents.FLAGS.GUILD_MEMBERS,
    Intents.FLAGS.GUILD_MESSAGES,
  ],
  partials: ['CHANNEL', 'MESSAGE', 'USER', 'GUILD_MEMBER'],
  allowedMentions: {
    parse: ['users'],
    repliedUser: false
  }
});



const ms = require("ms");
const fs = require('fs');
const { Manager } = require("moonlink.js");
const config = require('./config.json');
const { owners, prefix, emco, useEmbeds, Support, logChannelId} = require(`${process.cwd()}/config`);
const fetch = require("node-fetch");
client.prefix = prefix;
module.exports = client;
client.commands = new Collection();
client.slashCommands = new Collection();
client.config = require(`${process.cwd()}/config`);
require("./handler")(client);
const tempData = new Collection();
tempData.set("bots", []);







client.once('ready', () => {
  console.log(`Logged in as ${client.user.tag}`);
  setInterval(checkSubscriptions, 30000);
});

// دالة للتحقق من حالة الاشتراكات
function checkSubscriptions() {
  try {
    const logs = fs.readFileSync('./logs.json', 'utf8');
    const logsArray = JSON.parse(logs);

    const logChannel = client.channels.cache.find(channel => channel.id === logChannelId);

    logsArray.forEach((log, index) => {
      const remainingTime = log.expirationTime - Date.now();
      if (remainingTime <= 0) {
        const user = client.users.cache.get(log.user);
        if (user) {
          // إرسال معلومات الاشتراك في رسالة خاصة للشخص
          user.send( {
            files: ['لقد تم اضافه اشتراك من لوك ستور لك للتفاصيل -vip'],
          });
    

          
          const mention = `\`🔔\` - **Notice: <@&${Support}> **`;
          const embed = new MessageEmbed()
            .setTitle('Anend Subscription Details')
            .setThumbnail("https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683d61f1&is=683c1071&hm=273c4845b5715d5b473f88b26b17c99192f9106d58542f86ba28c718012b4641&=&format=webp&quality=lossless&width=461&height=461")
            .setDescription(`**UserID:** \`${user.id}\`\n**Username:** \`${user.username}\` / <@${user.id}>\n**ServerId**: \`${log.server}\`\n**Number of Bots:** \`${log.botsCount}\`\n**Subscription Time:** \`${log.subscriptionTime}\`\n**Expiration Time:** \`${new Date(log.expirationTime).toLocaleString()}\`\n**Code:** \`${log.code}\``)
            .setColor(emco);
          
          logChannel.send({ content: mention, embeds: [embed] });
           
        }
        
        logsArray.splice(index, 1);

        // استخراج التوكنات المرتبطة بالاشتراك المحذوف
        const tokens = fs.readFileSync('./tokens.json', 'utf8');
        const tokensArray = JSON.parse(tokens);

        const tokensToRemove = tokensArray.filter(tokenEntry => tokenEntry.code === log.code);

        // إضافة التوكنات إلى ملف bots.json
        const bots = fs.readFileSync('./bots.json', 'utf8');
        const botsArray = JSON.parse(bots);

        tokensToRemove.forEach(tokenEntry => {
          botsArray.push({
            token: tokenEntry.token,
            Server: null,
            channel: null,
            chat: null,
            status: null,
            client: null,
            useEmbeds: false
          });
        });

        fs.writeFileSync('./bots.json', JSON.stringify(botsArray, null, 2));

        // حذف التوكنات من ملف tokens.json
        const updatedTokensArray = tokensArray.filter(tokenEntry => !tokensToRemove.includes(tokenEntry));
        fs.writeFileSync('./tokens.json', JSON.stringify(updatedTokensArray, null, 2));
      }
    });

    // تحديث ملف السجلات بعد حذف الاشتراك
    fs.writeFileSync('./logs.json', JSON.stringify(logsArray, null, 2));
  } catch (error) {
    console.error('❌>', error);
  }
}




setTimeout(async () => {
  var data = fs.readFileSync('./tokens.json');
  var parsedData = JSON.parse(data);
  var tokens_data = parsedData;
  if (!tokens_data[0]) return;

  tokens_data.forEach(token => {
    runBotSystem(token.token);
  });
}, 3000);

async function convert(harinder) {
  try {
    const temperance = await fetch(harinder);
    const myrtte = temperance.url;
    if (myrtte) {
      return `${""}${myrtte}${""}`;
    } else {
      return null;
    }
  } catch (deari) {
    return 0;
  }
}


async function runBotSystem(token) {
  const client83883 = new Client({
    intents: [
      Intents.FLAGS.GUILDS,
      Intents.FLAGS.GUILD_MEMBERS,
      Intents.FLAGS.GUILD_MESSAGES,
      Intents.FLAGS.GUILD_VOICE_STATES
    ],
    partials: ['CHANNEL', 'GUILD_MEMBER'],
    allowedMentions: {
      parse: ['users'],
      repliedUser: false
    }
  });
 


  client83883.manager = new Manager({
    nodes: [
      {
        identifier: "main",
        host: "localhost",
        port: 2333,
        password: "youshallnotpass",
        secure: false,
      }
    ],
    sendPayload: (guildId, payload) => {
      const guild = client83883.guilds.cache.get(guildId);
      if (guild) guild.shard.send(JSON.parse(payload));
    },
    defaultVolume: 50,
    autoPlay: false
  });



  const skipButton = new MessageButton()
  .setCustomId('skipButton')
  .setEmoji("<:skipstart_1:1224082806741930108>")
  .setStyle('SECONDARY');
const volumeUpButton = new MessageButton()
  .setCustomId('volumeUpButton')
  .setEmoji("<:highvolume:1224081870409564231>")
  .setStyle('SECONDARY');
  const stopButton = new MessageButton()
  .setCustomId('pauseButton')
  .setEmoji("<:pause:1224080944013770822>")  // تغيير الإيموجي للإشارة للإيقاف المؤقت
  .setStyle('SECONDARY');
  const volumeDownButton = new MessageButton()
  .setCustomId('volumeDownButton')
  .setEmoji("<:lowvolume:1224079426564788274>")
  .setStyle('SECONDARY');
  const repeatButton = new MessageButton()
  .setCustomId('repeatButton')
  .setEmoji("<:undoarrow:1224078115479883816>")
  .setStyle('SECONDARY');
const row = new MessageActionRow()


  .addComponents(repeatButton, volumeDownButton, stopButton, volumeUpButton, skipButton);



  
  client83883.lastVolume = 50;

  // Helper function to format duration
  function formatDuration(duration) {
    const hours = Math.floor(duration / 3600000);
    const minutes = Math.floor((duration % 3600000) / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  // Moonlink event handlers
  client83883.manager.on("trackStart", (player, track) => {
    const embed = new MessageEmbed()
      .setAuthor("<:1327148990961287188:1327148990961287188> Playing song")
      .setColor(emco)
      .addFields(
        { name: 'Song Name', value: `***Started:* [${track.title}](${track.uri})**` },
        { name: 'Song Duration', value: `(\`${formatDuration(track.duration)}\`)` }
      )
      .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205557078890905610/ddddd.png?ex=65d8cd85&is=65c65885&hm=c45afc56ea3abbc91d3cac1215ec2698e45a5727f5fa5ad9e958b1a8e3c87bef&")
      .setFooter(client83883.user.username, client83883.user.displayAvatarURL());

    const textChannel = client83883.channels.cache.get(player.textChannelId);
    if (textChannel) {
      if (useEmbeds) {
        textChannel.send({ embeds: [embed], components: [row] }).catch(() => 0);
      } else {
        textChannel.send({
          content: `_Now playing :_ **${track.title}** _Time:_ **${formatDuration(track.duration)}**.`,
          components: [row]
        }).catch(() => 0);
      }
    }

    // Set volume if needed
    if (player.volume !== client83883.lastVolume) {
      player.setVolume(client83883.lastVolume);
    }
  })
  .on("trackAdd", (player, track) => {
    const embed = new MessageEmbed()
      .setAuthor("ϟ Adding to queue")
      .setColor(emco)
      .addFields(
        { name: 'Song Name', value: `**${track.title}**` },
        { name: 'Song Duration', value: `(\`${formatDuration(track.duration)}\`)` }
      )
      .setThumbnail("https://media.discordapp.net/attachments/1362373235458445605/1377384410252312586/Oryn_8.png?ex=683d61f1&is=683c1071&hm=273c4845b5715d5b473f88b26b17c99192f9106d58542f86ba28c718012b4641&=&format=webp&quality=lossless&width=461&height=461")
      .setFooter(client83883.user.username, client83883.user.displayAvatarURL());

    const textChannel = client83883.channels.cache.get(player.textChannelId);
    if (textChannel) {
      if (useEmbeds) {
        textChannel.send({ embeds: [embed] }).catch(() => 0);
      } else {
        textChannel.send({
          content: `_Added :_ **${track.title} \`(${formatDuration(track.duration)})\`** _Song to Queue_`
        }).catch(() => 0);
      }
    }
  })
  .on("playlistAdd", (player, playlist) => {
    const textChannel = client83883.channels.cache.get(player.textChannelId);
    if (textChannel) {
      if (useEmbeds) {
        const embed = new MessageEmbed()
        .setColor(emco)
          .setDescription(`<:1327148990961287188:1327148990961287188> **أُضيفت قائمة الآغاني** *${playlist.name}* (\`${playlist.tracks.length}\` آغنية) **إلى طابور الأغاني**`);
        textChannel.send({ embeds: [embed] }).catch(() => 0);
      } else {
        textChannel.send(`<:1327148990961287188:1327148990961287188> **أُضيفت قائمة الآغاني** *${playlist.name}* (\`${playlist.tracks.length}\` آغنية) **إلى طابور الأغاني**`).catch(() => 0);
      }
    }
  })
  .on("playerError", (player, error) => {
    console.log(error);
    const textChannel = client83883.channels.cache.get(player.textChannelId);
    if (textChannel) {
      if (useEmbeds) {
        const embed = new MessageEmbed()
        .setColor(emco)
          .setDescription(`<:1327148990961287188:1327148990961287188> **تم إستقبال خطأ:** ${error.toString().slice(0, 1974)}`);
        textChannel.send({ embeds: [embed] }).catch(() => 0);
      } else {
        textChannel.send(`<:1327148990961287188:1327148990961287188> **تم إستقبال خطأ:** ${error.toString().slice(0, 1974)}`).catch(() => 0);
      }
    } else {
      console.error(error);
    }
  });

  client83883.on('interactionCreate', async interaction => {
    if (!interaction.isButton()) return;

    const player = client83883.manager.players.get(interaction.guildId);

    if (!player) {
      await interaction.reply({ content: '***There is no song currently playing.***', ephemeral: true });
      return;
    }
  

    
    switch (interaction.customId) {
      case 'repeatButton':
        if (player.loop === "off") {
          player.setLoop("track");
          await interaction.reply({ content: '_Repeat mode set to :_ **ON**', ephemeral: true });
        } else {
          player.setLoop("off");
          await interaction.reply({ content: '_Repeat mode set to :_ **OFF**', ephemeral: true });
        }
        break;

      case 'volumeDownButton':
        const newVolumeDown = Math.max(0, player.volume - 10);
        player.setVolume(newVolumeDown);
        client83883.lastVolume = newVolumeDown;
        await interaction.reply({ content: `***ϟ Volume changed to \`${newVolumeDown}%\` .***`, ephemeral: true });
        break;

      case 'pauseButton':
        if (player.paused) {
          player.pause(false);
          await interaction.reply({ content: '***song has resumed.***', ephemeral: true });
        } else {
          player.pause(true);
          await interaction.reply({ content: '***song has been paused.***', ephemeral: true });
        }
        break;

      case 'volumeUpButton':
        const newVolumeUp = Math.min(150, player.volume + 10);
        player.setVolume(newVolumeUp);
        client83883.lastVolume = newVolumeUp;
        await interaction.reply({ content: `***volume has been raised to \`${newVolumeUp}%\` .***`, ephemeral: true });
        break;

      case 'skipButton':
        if (player.queue.length === 0) {
          await interaction.reply({ content: '*Server queue is empty.*', ephemeral: true });
          return;
        }
        player.skip();
        await interaction.reply({ content: '***ϟ Skipped the current song.***', ephemeral: true });
        break;
    
      default:
        await interaction.reply({ content: 'الزر غير معرف.', ephemeral: true });
        break;
    }
  });
  







  client83883.on('ready', async () => {
    let newData = tempData.get("bots");
    newData.push(client83883);
    tempData.set(`bots`, newData);

    let botNumber = newData.indexOf(client83883) + 1;
    console.log(`🎶 ${botNumber} > ${client83883.user.username} : ${client83883.guilds.cache.first()?.name}`);

    // Initialize Moonlink
    client83883.manager.init(client83883.user.id);
  });

  // Raw event handler for Moonlink
  client83883.on("raw", (packet) => {
    client83883.manager.packetUpdate(packet);

    let int = setInterval(async () => {
        var data = fs.readFileSync('./tokens.json', 'utf8');
        if (!data || data == '') return;
        data = JSON.parse(data);
        tokenObj = data.find((tokenBot) => tokenBot.token == token);
        if (!tokenObj) {
            client83883.destroy()?.catch(() => 0);
            return clearInterval(int);
        };

        let serverID = tokenObj.Server; // استخراج الـ ID للسيرفر من ملف التوكنات

        if (tokenObj.channel) {
            let guild = client83883.guilds.cache.get(serverID);
            if (guild) {
                let voiceChannel = guild?.me.voice.channel;
                if (!voiceChannel) {
                    let musicChannel = guild.channels.cache.get(tokenObj?.channel);
                    if (musicChannel && musicChannel.joinable) {
                        const player = client83883.manager.createPlayer({
                            guildId: guild.id,
                            voiceChannelId: musicChannel.id,
                            textChannelId: musicChannel.id,
                            selfDeaf: true
                        });
                        try {
                            player.connect();
                        } catch (error) {
                            console.log('Connection error:', error);
                        }
                    }
                }
                if (voiceChannel && voiceChannel.id !== tokenObj.channel) {
                    let musicChannel = guild.channels.cache.get(tokenObj?.channel);
                    if (musicChannel && musicChannel.joinable) {
                        const player = client83883.manager.createPlayer({
                            guildId: guild.id,
                            voiceChannelId: musicChannel.id,
                            textChannelId: musicChannel.id,
                            selfDeaf: true
                        });
                        try {
                            player.connect();
                        } catch (error) {
                            console.log('Connection error:', error);
                        }
                    }
                }
            }
        } else {
            let guild = client83883.guilds.cache.get(serverID);
            if (guild) {
                let voiceChannel = guild?.me.voice.channel;
                if (voiceChannel) {
                    const player = client83883.manager.players.get(guild.id);
                    if (player) {
                        player.destroy();
                    }
                }
            }
        }
    }, 5000);
});



client83883.on('messageCreate', async (message) => {
  if (message.author.bot || !message.guild) return;
  var data = fs.readFileSync('./tokens.json', 'utf8');
  if (data == '' || !data) return;
  data = JSON.parse(data);
  let tokenObj = data.find((t) => t.token == token);
  if (!data || !tokenObj) return;
  
  let args = message.content?.trim().split(' ');
  if (args) {
      if (args[0] == `<@!${client83883.user.id}>` || args[0] == `<@${client83883.user.id}>`) {
          args = args.slice(1);
          if (!args[0]) return;
          if (args[0] == 'help') {
            const botOwnerId = tokenObj.client; // استخراج الأيدي من ملف التوكنات
          
            const button1 = new MessageButton()
              .setLabel('Oryn Store')
              .setStyle('LINK')
              .setURL('https://discord.gg/D2GuZNGwuh')
          
            const row1 = new MessageActionRow().addComponents(button1);
          
            const helpEmbed = new MessageEmbed()
            .setAuthor('Oryn', client83883.user.displayAvatarURL({ dynamic: true }))
            
            .setThumbnail("https://media.discordapp.net/attachments/1362373235458445605/1377384410839650366/Oryn_9.png?ex=683d61f2&is=683c1072&hm=c4cbe47f27039b844f24d50c5c9923adca04d9ca195807f60761c4138b901315&=&format=webp&quality=lossless&width=864&height=486")
            .setTitle('*Music Commands:*')

            .setDescription(`
            
play [track] - Adds the track to the queue.  
search [track] - Searching from YouTube  

join - Joins the voice channel  
leave - Leaves the voice channel  
pause - Pauses the playback  
resume - Resumes the playback  

skip - Skips the currently playing track  
queue - Displays the current queue  
stop - Stop playing songs  
autoplay - Play songs on the first song  
nowplaying - Displays the currently playing track  
seek [timestamp] - Sets the track's position to the timestamp  
remove [position] - Removes the track from the queue  
loop [ON/OFF] - Repeat play song  
forward [Time] - Present a specific time of the song  
volume [volume] - Sets the bot's volume  
filter [filters] - Choose a filter to the song  

Owner Commands

setname [name] - Sets the name of the bot  
setavatar [attach a picture] - Sets the avatar of the bot  
streaming - Sets the status the bot displays  

setprefix [setprefix/unsetprefix] - Add and delete prefix  
setvc [setvc/leave] - Set the voice bot and name it as voice  
settc [settc/unchat] - Sets the text channel for playing music  
allcome [comeall/allcome] - Bring all bots to your room  

!vip - Control all bots in a server in a Oryn Store 

         
`)
    
            
              .setFooter('اذا يوجد مشكله تواصل مع : الدعم الفني للوك ستور >.');
          
              const additionalEmbed = new MessageEmbed()
              .setDescription(`
              <:1327148990961287188:1327148990961287188> **Owner:** <@${botOwnerId}>
              <:1327148990961287188:1327148990961287188> **Owner Id:** ${botOwnerId}`);
              

            message.author.send({
              embeds: [helpEmbed,additionalEmbed],
              components: [row1],
            }).then(async () => {
              message.react("✅").catch(() => 0);
            }).catch(() => {
              message.react("🔒").catch(() => 0);
            });
          }


        if (!owners.includes(message.author.id) && !message.member.permissions.has('ADMINISTRATOR')) {
          return;
        }     
             if(args[0] == 'restart' || args[0] == 'اعاده') {
             await client83883.destroy()
             setTimeout(async() => {
               client83883.login(token).then(() => {
                 message.react(`💹`).catch(() => 0)
             }).catch(() => { console.log(`${client83883.user.tag} (${client83883.user.id}) has an error with restarting.`) })
           }, 5000)
              
           } else if (args[0] == 'setname' || args[0] == 'اسم'|| args[0] == 'name' || args[0] == 'sn') {
            let name = args.slice(1).join(' ');
            if (!name) return;
        
            const tryChangeName = (newName, attempts = 0) => {
                client83883.user.setUsername(newName).then(async () => {
                    message.react('✅').catch(() => 0);
                }).catch((error) => {
                    if (error.code === 50035) { // الإسم مُستخدم كثيرًأ
                        if (attempts < 3) { // تحديد عدد المحاولات المسموح بها
                            const newNameWithDot = `${newName}.`; // إضافة نقطة إلى الإسم
                            tryChangeName(newNameWithDot, attempts + 1); // المحاولة مرة أخرى مع الإسم المُعدل
                        } else {
                            message.react('⏳').catch(() => 0); // تفاعل خطأ عند الوصول للحد الأقصى للمحاولات
                        }
                    } else {
                        console.error(error);
                        message.reply("An error occurred while changing the bot's name.");
                    }
                });
            };
        
            tryChangeName(name); // بدء المحاولة لتغيير الإسم
  
           } else if (args[0] == 'setavatar' || args[0] == 'صورة' || args[0] == 'avatar' || args[0] == 'avatar' || args[0] == 'sa') {
            let url = args[1];
            if (!url && !message.attachments.first()) return;
          
            if (message.attachments.first()) {
              url = message.attachments.first().url;
            }
          
            client83883.user.setAvatar(url)
              .then(() => {
                message.react('✅').catch(() => {});
              })
              .catch((error) => {
                message.react('✅').catch(() => {});
              });
                    
           } else if (args[0] == 'join' || args[0] == 'leave' || args[0] == 'setchannel' || args[0] == 'come' || args[0] == 'تعال' || args[0] == 'ادخل'|| args[0] == 'اخرج'|| args[0] == 'اطلع'|| args[0] == 'disablechannel' ) {
            let data = fs.readFileSync('./tokens.json');
            data = JSON.parse(data);
            tokenObj = data.find((tokenBot) => tokenBot.token == token);
            let channel;
            if (args[0] == 'join' || args[0] == 'come' || args[0] == 'setvc' || args[0] == 'ادخل' || args[0] == 'تعال')  {
              channel = message.member.voice.channel;
              if (!channel) return;
            } else {
              channel = await message.guild.channels.fetch(args[1]).catch(() => 0);
              if (!channel) return;
            }
            data = data.map((tokenBot) => {
              if (tokenBot.token == token) {
                tokenBot.channel = channel.id;
              }
              return tokenBot;
            });
            fs.writeFile('./tokens.json', JSON.stringify(data, null, 2), (err) => {
              if (err) throw err;
            });
            message.react('✅');
          }  
          else if (args[0] == 'setup') {
            let channel = message.member.voice.channel;
            if (!channel) return;
          
            data = data.map((tokenBot) => {
              if (tokenBot.token == token) {
                tokenBot.channel = channel.id;
              }
              return tokenBot;
            });
          
            // تغيير اسم البوت نفسه
            const cooldownTime = 5000; // تعيين فترة الانتظار بالمللي ثانية (5 ثوانٍ)
            const lastChangeTime = client83883.user.lastChangeTime || 0;
            const currentTime = Date.now();
          
            // إذا مضت فترة زمنية أقل من cooldownTime من آخر مرة تم فيها تغيير الاسم
            if (currentTime - lastChangeTime < cooldownTime) {
              return message.react('⏳');
            }
          
            try {
              await client83883.user.setUsername(channel.name);
              // تحديث وقت آخر تغيير
              client83883.user.lastChangeTime = Date.now();
              fs.writeFile('./tokens.json', JSON.stringify(data, null, 2), (err) => {
                if (err) throw err;
              });
              message.react('✅');
            } catch (error) {
              if (error.code === 50035) {
                // يتم إعادة المحاولة في حالة حدوث خطأ 50035
                return message.react('⏳');
              } else {
                console.error(error);
              }
            }
                  
          } else if (args[0] == 'setchat' || args[0] == 'chat' || args[0] == 'settc' || args[0] == 'اوامر') {
            let data = fs.readFileSync('./tokens.json', 'utf8');
            let parsedData = JSON.parse(data);
            
            tokenObj = parsedData.find((tokenBot) => tokenBot.token == token);
            
            if (!tokenObj) return;
            
            let channel = message.guild.channels.cache.get(message.channel.id); // Get the channel where the command was executed
            
            if (!channel) return;
            
            parsedData = parsedData.map((tokenBot) => {
                if (tokenBot.token == token) {
                    tokenBot.chat = channel.id; // Set the channel ID as the chat channel
                }
                return tokenBot;
            });
            
            fs.writeFile('./tokens.json', JSON.stringify(parsedData, null, 2), (err) => {
                if (err) throw err;
                message.react('✅');
            });        
          
          } else if (args[0] == 'unchat' || args[0] == 'unt' || args[0] == 'الغاء') {
            let data = fs.readFileSync('./tokens.json', 'utf8');
            let parsedData = JSON.parse(data);
            
            tokenObj = parsedData.find((tokenBot) => tokenBot.token == token);
            
            if (!tokenObj) return;
            
            let channelId = tokenObj.chat;
            if (!channelId) return message.reply('**لا يوجد شات مُحدد.**');
                
            parsedData = parsedData.map((tokenBot) => {
                if (tokenBot.token == token) {
                    delete tokenBot.chat; // Remove the chat ID
                }
                return tokenBot;
            });
            
            fs.writeFile('./tokens.json', JSON.stringify(parsedData, null, 2), (err) => {
                if (err) throw err;
                message.react('✅');
            });
            loadPrefix();

        } else if (args[0] == 'ping' || args[0] == 'بنج' || args[0] == 'بنغ') {
            const ping = client.ws.ping;
            message.reply(`***ϟ Pong! My ping is ${ping}ms.***`);
            
          }else if (args[0] === 'setting' || args[0] == 'st' || args[0] == 'اعدادات' || args[0] == 'معلومات' || args[0] == 'settings') {
            let voiceChannel = message.guild.channels.cache.get(tokenObj.channel);
            let commandChat = message.guild.channels.cache.get(tokenObj.chat);
                        const embed = new MessageEmbed()
              .setThumbnail(client83883.user.displayAvatarURL({ dynamic: true }))
              .setColor(emco) 
              .setDescription(`
                **Platform :** \`YouTube\`
                **Voice Channel :** ${voiceChannel ? `<#${voiceChannel.id}>` : '`Not set`'}
                **Text Channel :** ${commandChat ? `<#${commandChat.id}>` : '`Not set`'}
                **Play In Voice Channel :** \`Disable\`
                **Number of Servers the Bot is in :** \`${client83883.guilds.cache.size}\`
              `);
            
            message.reply({ embeds: [embed] });

          } else if (args[0] == 'setstreaming' || args[0] == 'streaming' || args[0] == 'ste' || args[0] == 'ستريمنج') {
            let status = message.content.split(" ")[2];
            if (!status) return message.react("❌");
            client83883.user.setPresence({
              activities: [
                {
                  name: status,
                  type: 'STREAMING',
                  url: "https://twitch.tv/orynStore" + status,
                },
              ],
              status: 'online',
            });
            message.react("✅");
          
            // Save the new status in the token file
            let tokens = fs.readFileSync('./tokens.json');
            tokens = JSON.parse(tokens);
            let tokenObj = tokens.find((tokenBot) => tokenBot.token == token);
            tokenObj.status = status;
            fs.writeFileSync('./tokens.json', JSON.stringify(tokens, null, 2));
          } else if (args[0] == 'setprefix') {
            if (!args[1]) return message.reply('يرجى تحديد البادئة الجديدة للبوت.');
  
            let newPrefix = args[1];  
            
            let data = fs.readFileSync('./tokens.json', 'utf8');
            let parsedData = JSON.parse(data);
            let tokenObj = parsedData.find((tokenBot) => tokenBot.token === token);
            if (tokenObj) {
                tokenObj.prefix = newPrefix;  
            } else {
                parsedData.push({ token, prefix: newPrefix });  
            }
            fs.writeFileSync('./tokens.json', JSON.stringify(parsedData, null, 2));
            
            message.reply(`**تم تحديث بادئه إلي : \`${newPrefix}\`**`);

        } else if (args[0] == 'addrole') {
          if (!message.mentions.roles.size) {
              return message.reply('يرجى منشن الرول لتحديد الإيدي.');
          }
      
          const role = message.mentions.roles.first();
          const roleId = role.id;
      
          let data = fs.readFileSync('./tokens.json', 'utf8');
          let parsedData = JSON.parse(data);
          
          tokenObj = parsedData.find((tokenBot) => tokenBot.token == token);
          
          if (!tokenObj) return;
          
          // حفظ أيدي الرول مع التوكن
          parsedData = parsedData.map((tokenBot) => {
              if (tokenBot.token == token) {
                  tokenBot.Admin = roleId;
              }
              return tokenBot;
          });
          
          fs.writeFile('./tokens.json', JSON.stringify(parsedData, null, 2), (err) => {
              if (err) throw err;
              message.react('✅');
          });
      }
      
          
        }
      }
    });

    


    client83883.on("ready", () => {
      // تحديد وظيفة للشيك على حالة التوكنات كل 10 ثوانٍ
      setInterval(() => {
        fs.readFile('./tokens.json', 'utf8', (err, data) => {
          if (err) {
            console.error(err);
            return;
          }
          const tokens = JSON.parse(data);
          tokens.forEach((token) => {
            // إذا كان هناك حالة محددة للتوكن الحالي، قم بتعيين حالة البوت بناءً عليها
            if (token.status && token.token === client83883.token) {
              client83883.user.setPresence({
                activities: [
                  {
                    name: token.status,
                    type: 'STREAMING',
                    url: "https://twitch.tv/" + token.status,
                  },
                ],
                status: 'online',
              });
            }
          });
        });
      }, 10000); // الشيك كل 10 ثوانٍ
    });
    
    

  
// -----------------------------------------------------------

client83883.on("ready", () => { client83883.user.setPresence({ status: 'dnd', activities: [{ name: `Oryn Story.`, type: "STREAMING", url: "https://www.stwitch.tv/Oryn" }] }); });



client83883.on('guildCreate', async (guild) => {
  let tokens = [];
  try {
    const tokensData = fs.readFileSync('./tokens.json', 'utf8');
    tokens = JSON.parse(tokensData);
  } catch (error) {
    console.error('Error reading tokens.json:', error);
    return;
  }

  const guildId = guild.id;
  const botName = client83883.user.tag; // اسم البوت
  if (!tokens.some(token => token.Server === guildId)) {
    await guild.leave();
    console.log(`\x1b[31m > left server ${botName} : ${guild.name}\x1b[0m`);
  } else {
    console.log(`\x1b[32m✅ > joined server ${botName} : ${guild.name}\x1b[0m`);
  }
});


// -----------------------------------------------------------
client83883.on("messageCreate", async (message) => {
  if (message.author.bot || !message.guild) return;
  let member_voice = message.member?.voice?.channel;
  if (!member_voice) return;
  let client_voice = message.guild.me?.voice?.channel;
  if (!client_voice) return;
  if (member_voice.id !== client_voice.id) return;

  var data = fs.readFileSync('./tokens.json', 'utf8');
  if (!data || data == '') return;
  data = JSON.parse(data);
  if (!data) return;

  let botData = data.find((tok) => tok.token == token);
  if (!botData) return;

  let prefix = botData.prefix || "";

      let cmdsArray = {
      play: [`${prefix}شغل`, `${prefix}ش`, `${prefix}p`, `${prefix}play`, `${prefix}P`, `${prefix}Play`],
      stop: [`${prefix}stop`, `${prefix}وقف`, `${prefix}Stop`, `${prefix}توقيف`],
      skip: [`${prefix}skip`, `${prefix}سكب`, `${prefix}تخطي`, `${prefix}s`, `${prefix}س`, `${prefix}S`, `${prefix}Skip`],
      volume: [`${prefix}volume`, `${prefix}vol`, `${prefix}صوت`, `${prefix}v`, `${prefix}ص`,`${prefix}V`,`${prefix}Vol`,`${prefix}Volume`],
      nowplaying: [`${prefix}nowplaying`, `${prefix}np`,`${prefix}Np`,`${prefix}Nowplaying`,`${prefix}الشغال`,`${prefix}الان`],
      loop: [`${prefix}loop`, `${prefix}تكرار`, `${prefix}l`,`${prefix}L`,`${prefix}Loop`],
      pause: [`${prefix}pause`, `${prefix}توقيف`, `${prefix}كمل`, `${prefix}pa`,`${prefix}Pa`,`${prefix}Pause`],
      seek: [`${prefix}seek`,`${prefix}Seek`,`${prefix}قدم`,`${prefix}se`,`${prefix}Se`],
      forward: [`${prefix}forward`,`${prefix}Forward`,`${prefix}تقديم`,`${prefix}fo`,`${prefix}Fo`],
      autoplay: [`${prefix}autoplay`,`${prefix}Autoplay`,`${prefix}Ap`,`${prefix}ap`],
      queue: [`${prefix}queue`, `${prefix}قائمة`, `${prefix}اغاني`, `${prefix}q`, `${prefix}qu`,`${prefix}Q`,`${prefix}Qu`,`${prefix}Queue`],
  };

    if (cmdsArray.play.some((cmd) => message.content.split(' ')[0] == cmd)) {
      let song = message.content.split(' ').slice(1).join(' ')
      if (song) {
        try {
          // Get or create player
          const player = client83883.manager.createPlayer({
            guildId: message.guild.id,
            voiceChannelId: message.member.voice.channel.id,
            textChannelId: message.channel.id,
            selfDeaf: true
          });

          // Connect to voice channel if not already connected
          if (!player.connected) await player.connect();

          // Search for the track
          const result = await client83883.manager.search({
            query: String(await convert(song) || song)
          });

          if (!result || !result.tracks || result.tracks.length === 0) {
            return message.reply('❌ No results found for your query!');
          }

          // Add track(s) to queue
          const track = result.tracks[0];
          player.queue.add(track);

          if (useEmbeds) {
            const embed = new MessageEmbed()
              .setDescription(`***ϟ It will start operating .....***`)
              .setColor(emco);
            message.reply({ embeds: [embed] });
          } else {
            message.reply(`***ϟ It will start operating .....***`);
          }

          // Play track if not already playing
          if (!player.playing && !player.paused) {
            await player.play();
          }
        } catch (error) {
          console.error('Error playing track:', error);
          message.reply('❌ An error occurred while trying to play the track.');
        }
      } else {
        if (useEmbeds) {
          const embed = new MessageEmbed()
          .setAuthor("اوامر التشغيل:")
            .setDescription(`***\`play [ title ]\` :** plays first result from **YouTube***.\n***\`play [URL]\` :** searches **YouTube, Spotify**, **SoundCloud***.`)
            .setColor(emco)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205555540814594118/njmm.png?ex=65d8cc17&is=65c65717&hm=e0b16d27ffb11e7d6b45d8bdfba919846783c48d186afac4e0abab16d9ac679d&");
          message.reply({ embeds: [embed] }).catch(() => 0);
        } else {
          message.reply(`*Play command usage:*\n***play [ title ] :** plays first result from **YouTube***.\n***play [URL]:** searches **YouTube, Spotify**, **SoundCloud***.`).catch(() => 0);
        }
      }
    }  else if (cmdsArray.seek.some((cmd) => message.content.split(" ")[0] == cmd)) {
      let args = message.content.split(" ");
      const player = client83883.manager.players.get(message.guild.id);
      if (!player) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`**<:1327148990961287188:1327148990961287188> يجب ان تكون اغنيه مشغله لاتسخدام هذا الامر!**`)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205555540814594118/njmm.png?ex=65d8cc17&is=65c65717&hm=e0b16d27ffb11e7d6b45d8bdfba919846783c48d186afac4e0abab16d9ac679d&")
            .setColor(emco);
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        const seconds = convertTimeToSeconds("10s");
        const time = parseInt(seconds);
        if (isNaN(time) || time === 0) {
          if (useEmbeds) {
            const embed = new MessageEmbed()
              .setDescription(`**❌ Invalid time provided!**`)
              .setColor(emco);
            message.channel.send({ embeds: [embed] }).catch(() => 0);
          } else {
            message.channel.send(`❌ Invalid time provided!`).catch(() => 0);
          }
          return;
        }
        player.seek(player.position + (time * 1000));
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`_Songs skipped :_ **${time}s**`)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205555936727539713/emoji.png?ex=65d8cc75&is=65c65775&hm=5c1ddf1e6f50a0ef35e378c8c8086f7e4ebae5661a536d3d7ff3c821bc53e6ea&")
            .setColor(emco);
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`_Songs skipped :_ **${time}s**`).catch(() => 0);
        }
      }
    } else if (cmdsArray.forward.some((cmd) => message.content.split(" ")[0] == cmd)) {
      let args = message.content.split(" ");
      const player = client83883.manager.players.get(message.guild.id);

      if (!player) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`**🎶 There must be music playing to use that!**`)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205555540814594118/njmm.png?ex=65d8cc17&is=65c65717&hm=e0b16d27ffb11e7d6b45d8bdfba919846783c48d186afac4e0abab16d9ac679d&")
            .setColor(emco);
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        if (!args[1]) {
          if (useEmbeds) {
            const embed = new MessageEmbed()
              .setDescription(`> Please type the number of seconds you want to seek forward`)
              .setColor(emco);
            message.channel.send({ embeds: [embed] }).catch(() => 0);
          } else {
            message.channel.send(`> Please type the number of seconds you want to seek forward`).catch(() => 0);
          }
          return;
        }

        const seconds = convertTimeToSeconds(args[1]);
        const time = parseInt(seconds);

        if (isNaN(time) || time === 0) {
          if (useEmbeds) {
            const embed = new MessageEmbed()
              .setDescription(`🚫 Attach a valid number!`)
              .setColor(emco);
            message.reply({ embeds: [embed] }).catch(() => 0);
          } else {
            message.reply(`🚫 Attach a valid number!`).catch(() => 0);
          }
          return;
        }

        const newPosition = player.position + (time * 1000);
        player.seek(newPosition);

        const formattedTime = formatTime(newPosition);
        const description = `_Song time :_ **${formattedTime}**`;

        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(description)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205556141032214618/111.png?ex=65d8cca6&is=65c657a6&hm=44e2be2c07211ae17c441738b34edecb7a090a411b30da2283c4712fe7131dea&")
            .setColor(emco);
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(description).catch(() => 0);
        }
      }

      function formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.round(seconds % 60);

        return `${hours < 10 ? '0' : ''}${hours}:${minutes < 10 ? '0' : ''}${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
      }

    } else if (cmdsArray.autoplay.some((cmd) => message.content.split(" ")[0] == cmd)) {
      const player = client83883.manager.players.get(message.guild.id);

      if (!player) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`**🎶 There must be music playing to use that!**`)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205555540814594118/njmm.png?ex=65d8cc17&is=65c65717&hm=e0b16d27ffb11e7d6b45d8bdfba919846783c48d186afac4e0abab16d9ac679d&")
            .setColor(emco);
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        // Toggle autoplay (Moonlink doesn't have built-in autoplay, so we'll simulate it)
        player.autoplay = !player.autoplay;

        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`_Autoplay mode set to :_ ${player.autoplay ? "**ON**" : "**OFF**"}`)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205556345647136920/waweda.png?ex=65d8ccd6&is=65c657d6&hm=64aa4bf559a31866f3c593be48603afc717a741b24b181d284fc5ce6a183848e&")
            .setColor(emco);
          message.reply({ embeds: [embed] }).catch(() => 0);
        } else {
          message.reply(`_Autoplay mode set to :_ ${player.autoplay ? "**ON**" : "**OFF**"}`).catch(() => 0);
        }
      }
    }
    
  
  
    else if (cmdsArray.stop.some((cmd) => message.content.split(' ')[0] == cmd)) {
      const player = client83883.manager.players.get(message.guild.id);
      if (!player) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`**🎶 There must be music playing to use that!**`)
            .setColor(emco)
            .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        player.destroy();

        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription("**ϟ Songs Has Been :** ***Stopped***")
            .setColor(emco)
            .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169710999268491325/NowPlaying.png?ex=65566542&is=6543f042&hm=00a5c0c58c2c36e143b5b778cc3681aea08c75b8458c413133a490343197ec7b&");
          message.reply({ embeds: [embed] }).catch(() => 0);
        } else {
          message.reply("ϟ **Stopped music, and the queue has been cleared**").catch(() => 0);
        }
      }
    } else if (cmdsArray.loop.some((cmd) => message.content.split(' ')[0] == cmd)) {
      const player = client83883.manager.players.get(message.guild.id);
      if (!player) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
          .setDescription(`**🎶 There must be music playing to use that!**`)
          .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
          .setColor(emco)
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        const newLoop = player.loop === "off" ? "track" : "off";
        player.setLoop(newLoop);
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`_Repeat mode set to :_ ${newLoop === "track" ? "**ON ..**" : "**OFF ..**"}`)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205556753752789043/Untitled-1.png?ex=65d8cd38&is=65c65838&hm=29f9c403050d6f24f661f21a34fd1604be145afdb38e181610c9685d1c6b72ff&")
            .setColor(emco)
          message.reply({ embeds: [embed] }).catch(() => 0);
        } else {
          message.reply(`_Repeat mode set to :_ ${newLoop === "track" ? "**ON ..**" : "**OFF ..**"}`).catch(() => 0);
        }
      }

    } else if (cmdsArray.pause.some((cmd) => message.content.split(' ')[0] == cmd)) {
      const player = client83883.manager.players.get(message.guild.id);
      if (!player) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
          .setDescription(`**🎶 There must be music playing to use that!**`)
          .setColor(emco)
          .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        if (player.paused) {
          player.pause(false);
          message.react("▶️").catch(() => 0);
        } else {
          player.pause(true);
          message.react("⏸️").catch(() => 0);
        }
      }
    } else if (cmdsArray.nowplaying.some((cmd) => message.content.split(' ')[0] == cmd)) {
      const player = client83883.manager.players.get(message.guild.id);
      if (!player || !player.queue.current) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
          .setDescription(`**🎶 There must be music playing to use that!**`)
          .setColor(emco)
          .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
      } else {
        const track = player.queue.current;
        const embed = new MessageEmbed()
          .setAuthor('Playing now', client83883.user.displayAvatarURL({ dynamic: true }))
          .setColor(emco)
          .setDescription(`**[${track.title}](${track.uri})**`)
          .setThumbnail(track.thumbnail)
          .setFooter(message.author.username, message.author.avatarURL());
        message.channel.send({ embeds: [embed] }).catch(() => 0);
      }
    } else if (cmdsArray.volume.some((cmd) => message.content.split(' ')[0] == cmd)) {
  const args = message.content.split(' ');
  const player = client83883.manager.players.get(message.guild.id);
  if (!player) {
    if (useEmbeds) {
      const embed = new MessageEmbed()
      .setDescription(`**🎶 There must be music playing to use that!**`)
      .setColor(emco)
      .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
      message.reply({ embeds: [embed] }).catch(() => 0);
    } else {
      message.reply(`🎶 There must be music playing to use that!`).catch(() => 0);
    }
  } else {
    if (!args[1]) {
      if (useEmbeds) {
        const embed = new MessageEmbed()
          .setDescription(`_🔊 Current volume is :_ **${player.volume}**`)
          .setColor(emco)
          .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1170057890506223647/4f4b99efc0371.png?ex=6557a853&is=65453353&hm=40e45c153b144474c1ca95c2854f3f21933cc20c1d2abc1f0ec1e8945da812ea&")
        message.reply({ embeds: [embed] }).catch(() => 0);
      } else {
        message.reply(`_🔊 Current volume is :_ **${player.volume}**`).catch(() => 0);
      }
    } else {
      const volume = parseInt(args[1]);
      if (isNaN(volume) || volume > 150 || volume < 0) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`🚫 Volume must be a valid integer between 0 and 150!`)
            .setColor(emco)
            .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`🚫 Volume must be a valid integer between 0 and 150!`).catch(() => 0);
        }
      } else {
        client83883.lastVolume = volume;
        player.setVolume(volume);
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`***ϟ Volume changed to \`${volume}%\` .***`)
            .setColor(emco)
            .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1170057890506223647/4f4b99efc0371.png?ex=6557a853&is=65453353&hm=40e45c153b144474c1ca95c2854f3f21933cc20c1d2abc1f0ec1e8945da812ea&");
          message.reply({ embeds: [embed] }).catch(() => 0);
        } else {
          message.reply(`*ϟ Volume changed to **\`${volume}%\`** .*`).catch(() => 0);
        }
      }
    }
  }
    } else if (cmdsArray.skip.some((cmd) => message.content.split(' ')[0] == cmd)) {
      const player = client83883.manager.players.get(message.guild.id);
      if (!player) return message.reply(`🎶 There must be music playing to use that!`).catch(() => 0);
      try {
        const currentTrack = player.queue.current;
        await player.skip();
        if (useEmbeds) {
          const embed = new MessageEmbed()
            .setDescription(`***ϟ Skipped ${currentTrack ? currentTrack.title : 'current song'}***`)
            .setColor(emco)
            .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205556141032214618/111.png?ex=65d8cca6&is=65c657a6&hm=44e2be2c07211ae17c441738b34edecb7a090a411b30da2283c4712fe7131dea&");
          message.channel.send({ embeds: [embed] }).catch(() => 0);
        } else {
          message.channel.send(`_skipped, the next song is now playing_`).catch(() => 0);
        }
      } catch (e) {
        if (player.queue.length === 0) {
          player.destroy();
          message.react(`✅`).catch(() => 0);
        } else {
          if (useEmbeds) {
            const embed = new MessageEmbed()
              .setColor(emco)
              .setDescription(`***ϟ Error skipping song***`);
            message.channel.send({ embeds: [embed] }).catch(() => 0);
          } else {
            message.channel.send(`***ϟ Error skipping song***`).catch(() => 0);
          }
        }
      }
    } if (cmdsArray.queue.some((cmd) => message.content.split(' ')[0] == cmd)) {
      const player = client83883.moon.players.get(message.guild.id);
      if (!player || (!player.queue.current && player.queue.length === 0)) {
        if (useEmbeds) {
          const embed = new MessageEmbed()
          .setDescription(`**🎶 There must be music playing to use that!**`)
          .setThumbnail("https://cdn.discordapp.com/attachments/1091536665912299530/1169712150130995220/no.png?ex=65566654&is=6543f154&hm=b95ef265828fafc88f4adc56d7ba9f07d44557c4ce8796c790313d889040eafb&")
          .setColor(emco)
          message.reply({ embeds: [embed] }).catch(() => 0);
        } else {
          message.reply(`🎶 There must be music playing to use that!`).catch(() => 0);
        }
        return;
      }

      let songNames = [];
      if (player.queue.current) {
        songNames.push(`\`1\`. ${player.queue.current.title}`);
      }

      if (player.queue.length > 0) {
        songNames = songNames.concat(
          player.queue.map((track, index) => `\`${index + 2}\`. ${track.title}`)
        );
      }

      const totalSongs = (player.queue.current ? 1 : 0) + player.queue.length;
      const songsDescription = songNames.join('\n');

      if (useEmbeds) {
        const embed = new MessageEmbed()
        .setAuthor(`ϟ Total songs :  ( ${totalSongs} )`)
          .setDescription(`*Now playing :* \n${songsDescription}`)
          .setThumbnail("https://cdn.discordapp.com/attachments/1161286178822176858/1205557078890905610/ddddd.png?ex=65d8cd85&is=65c65885&hm=c45afc56ea3abbc91d3cac1215ec2698e45a5727f5fa5ad9e958b1a8e3c87bef&")
          .setColor(emco)
          .setFooter({ text: `${client83883.user.username}`, iconURL: `${client83883.user.displayAvatarURL({ dynamic: true })}` });
        message.channel.send({ embeds: [embed] }).catch(() => 0);
      } else {
        message.channel.send(`**Queue (${totalSongs} tracks):**\n${songsDescription}`).catch(() => 0);
      }
    }
    
  });
  try {
    await client83883.login(token);
  } catch (e) {
    console.log(`❌ > ${token} ${e}`);
  }
};




process.on("uncaughtException", console.log);
process.on("unhandledRejection", console.log);
process.on("rejectionHandled", console.log);


