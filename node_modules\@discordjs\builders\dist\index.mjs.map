{"version": 3, "sources": ["../src/messages/embed/Assertions.ts", "../src/util/validation.ts", "../src/util/normalizeArray.ts", "../src/messages/embed/Embed.ts", "../src/messages/formatters.ts", "../src/components/Assertions.ts", "../src/components/selectMenu/SelectMenuOption.ts", "../src/components/ActionRow.ts", "../src/components/Component.ts", "../src/components/Components.ts", "../src/components/button/Button.ts", "../src/components/selectMenu/SelectMenu.ts", "../src/components/textInput/TextInput.ts", "../src/components/textInput/Assertions.ts", "../src/util/jsonEncodable.ts", "../src/interactions/modals/Assertions.ts", "../src/interactions/modals/Modal.ts", "../src/interactions/slashCommands/Assertions.ts", "../src/interactions/slashCommands/SlashCommandBuilder.ts", "../src/interactions/slashCommands/SlashCommandSubcommands.ts", "../src/interactions/slashCommands/mixins/NameAndDescription.ts", "../src/interactions/slashCommands/options/attachment.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionBase.ts", "../src/interactions/slashCommands/options/boolean.ts", "../src/interactions/slashCommands/options/channel.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionChannelTypesMixin.ts", "../src/interactions/slashCommands/options/integer.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandNumericOptionMinMaxValueMixin.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionWithChoicesAndAutocompleteMixin.ts", "../src/interactions/slashCommands/options/mentionable.ts", "../src/interactions/slashCommands/options/number.ts", "../src/interactions/slashCommands/options/role.ts", "../src/interactions/slashCommands/options/string.ts", "../src/interactions/slashCommands/options/user.ts", "../src/interactions/slashCommands/mixins/SharedSlashCommandOptions.ts", "../src/interactions/contextMenuCommands/Assertions.ts", "../src/interactions/contextMenuCommands/ContextMenuCommandBuilder.ts", "../src/util/equatable.ts", "../src/util/componentUtil.ts"], "sourcesContent": ["import { s } from '@sapphire/shapeshift';\nimport type { APIEmbedField } from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../../util/validation';\n\nexport const fieldNamePredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(256)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const fieldValuePredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(1024)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const fieldInlinePredicate = s.boolean.optional;\n\nexport const embedFieldPredicate = s\n\t.object({\n\t\tname: fieldNamePredicate,\n\t\tvalue: fieldValuePredicate,\n\t\tinline: fieldInlinePredicate,\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const embedFieldsArrayPredicate = embedFieldPredicate.array.setValidationEnabled(isValidationEnabled);\n\nexport const fieldLengthPredicate = s.number.lessThanOrEqual(25).setValidationEnabled(isValidationEnabled);\n\nexport function validateFieldLength(amountAdding: number, fields?: APIEmbedField[]): void {\n\tfieldLengthPredicate.parse((fields?.length ?? 0) + amountAdding);\n}\n\nexport const authorNamePredicate = fieldNamePredicate.nullable.setValidationEnabled(isValidationEnabled);\n\nexport const imageURLPredicate = s.string\n\t.url({\n\t\tallowedProtocols: ['http:', 'https:', 'attachment:'],\n\t})\n\t.nullish.setValidationEnabled(isValidationEnabled);\n\nexport const urlPredicate = s.string\n\t.url({\n\t\tallowedProtocols: ['http:', 'https:'],\n\t})\n\t.nullish.setValidationEnabled(isValidationEnabled);\n\nexport const embedAuthorPredicate = s\n\t.object({\n\t\tname: authorNamePredicate,\n\t\ticonURL: imageURLPredicate,\n\t\turl: urlPredicate,\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const RGBPredicate = s.number.int\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(255)\n\t.setValidationEnabled(isValidationEnabled);\nexport const colorPredicate = s.number.int\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(0xffffff)\n\t.or(s.tuple([RGBPredicate, RGBPredicate, RGBPredicate]))\n\t.nullable.setValidationEnabled(isValidationEnabled);\n\nexport const descriptionPredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(4096)\n\t.nullable.setValidationEnabled(isValidationEnabled);\n\nexport const footerTextPredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(2048)\n\t.nullable.setValidationEnabled(isValidationEnabled);\n\nexport const embedFooterPredicate = s\n\t.object({\n\t\ttext: footerTextPredicate,\n\t\ticonURL: imageURLPredicate,\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const timestampPredicate = s.union(s.number, s.date).nullable.setValidationEnabled(isValidationEnabled);\n\nexport const titlePredicate = fieldNamePredicate.nullable.setValidationEnabled(isValidationEnabled);\n", "let validate = true;\n\nexport const enableValidators = () => (validate = true);\nexport const disableValidators = () => (validate = false);\nexport const isValidationEnabled = () => validate;\n", "export function normalizeArray<T>(arr: RestOrArray<T>): T[] {\n\tif (Array.isArray(arr[0])) return arr[0];\n\treturn arr as T[];\n}\n\nexport type RestOrArray<T> = T[] | [T[]];\n", "import type { APIEmbed, APIEmbedAuthor, APIEmbedField, APIEmbedFooter, APIEmbedImage } from 'discord-api-types/v10';\nimport {\n\tcolorPredicate,\n\tdescriptionPredicate,\n\tembedAuthorPredicate,\n\tembedFieldsArrayPredicate,\n\tembedFooterPredicate,\n\timageURLPredicate,\n\ttimestampPredicate,\n\ttitlePredicate,\n\turlPredicate,\n\tvalidateFieldLength,\n} from './Assertions';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray';\n\nexport type RGBTuple = [red: number, green: number, blue: number];\n\nexport interface IconData {\n\t/**\n\t * The URL of the icon\n\t */\n\ticonURL?: string;\n\t/**\n\t * The proxy URL of the icon\n\t */\n\tproxyIconURL?: string;\n}\n\nexport type EmbedAuthorData = Omit<APIEmbedAuthor, 'icon_url' | 'proxy_icon_url'> & IconData;\n\nexport type EmbedAuthorOptions = Omit<EmbedAuthorData, 'proxyIconURL'>;\n\nexport type EmbedFooterData = Omit<APIEmbedFooter, 'icon_url' | 'proxy_icon_url'> & IconData;\n\nexport type EmbedFooterOptions = Omit<EmbedFooterData, 'proxyIconURL'>;\n\nexport interface EmbedImageData extends Omit<APIEmbedImage, 'proxy_url'> {\n\t/**\n\t * The proxy URL for the image\n\t */\n\tproxyURL?: string;\n}\n/**\n * Represents a embed in a message (image/video preview, rich embed, etc.)\n */\nexport class EmbedBuilder {\n\tpublic readonly data: APIEmbed;\n\n\tpublic constructor(data: APIEmbed = {}) {\n\t\tthis.data = { ...data };\n\t\tif (data.timestamp) this.data.timestamp = new Date(data.timestamp).toISOString();\n\t}\n\n\t/**\n\t * Adds fields to the embed (max 25)\n\t *\n\t * @param fields The fields to add\n\t */\n\tpublic addFields(...fields: RestOrArray<APIEmbedField>): this {\n\t\tfields = normalizeArray(fields);\n\t\t// Ensure adding these fields won't exceed the 25 field limit\n\t\tvalidateFieldLength(fields.length, this.data.fields);\n\n\t\t// Data assertions\n\t\tembedFieldsArrayPredicate.parse(fields);\n\n\t\tif (this.data.fields) this.data.fields.push(...fields);\n\t\telse this.data.fields = fields;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes, replaces, or inserts fields in the embed (max 25)\n\t *\n\t * @param index The index to start at\n\t * @param deleteCount The number of fields to remove\n\t * @param fields The replacing field objects\n\t */\n\tpublic spliceFields(index: number, deleteCount: number, ...fields: APIEmbedField[]): this {\n\t\t// Ensure adding these fields won't exceed the 25 field limit\n\t\tvalidateFieldLength(fields.length - deleteCount, this.data.fields);\n\n\t\t// Data assertions\n\t\tembedFieldsArrayPredicate.parse(fields);\n\t\tif (this.data.fields) this.data.fields.splice(index, deleteCount, ...fields);\n\t\telse this.data.fields = fields;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the embed's fields (max 25).\n\t * @param fields The fields to set\n\t */\n\tpublic setFields(...fields: RestOrArray<APIEmbedField>) {\n\t\tthis.spliceFields(0, this.data.fields?.length ?? 0, ...normalizeArray(fields));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the author of this embed\n\t *\n\t * @param options The options for the author\n\t */\n\n\tpublic setAuthor(options: EmbedAuthorOptions | null): this {\n\t\tif (options === null) {\n\t\t\tthis.data.author = undefined;\n\t\t\treturn this;\n\t\t}\n\n\t\t// Data assertions\n\t\tembedAuthorPredicate.parse(options);\n\n\t\tthis.data.author = { name: options.name, url: options.url, icon_url: options.iconURL };\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the color of this embed\n\t *\n\t * @param color The color of the embed\n\t */\n\tpublic setColor(color: number | RGBTuple | null): this {\n\t\t// Data assertions\n\t\tcolorPredicate.parse(color);\n\n\t\tif (Array.isArray(color)) {\n\t\t\tconst [red, green, blue] = color;\n\t\t\tthis.data.color = (red << 16) + (green << 8) + blue;\n\t\t\treturn this;\n\t\t}\n\t\tthis.data.color = color ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description of this embed\n\t *\n\t * @param description The description\n\t */\n\tpublic setDescription(description: string | null): this {\n\t\t// Data assertions\n\t\tdescriptionPredicate.parse(description);\n\n\t\tthis.data.description = description ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the footer of this embed\n\t *\n\t * @param options The options for the footer\n\t */\n\tpublic setFooter(options: EmbedFooterOptions | null): this {\n\t\tif (options === null) {\n\t\t\tthis.data.footer = undefined;\n\t\t\treturn this;\n\t\t}\n\n\t\t// Data assertions\n\t\tembedFooterPredicate.parse(options);\n\n\t\tthis.data.footer = { text: options.text, icon_url: options.iconURL };\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the image of this embed\n\t *\n\t * @param url The URL of the image\n\t */\n\tpublic setImage(url: string | null): this {\n\t\t// Data assertions\n\t\timageURLPredicate.parse(url);\n\n\t\tthis.data.image = url ? { url } : undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the thumbnail of this embed\n\t *\n\t * @param url The URL of the thumbnail\n\t */\n\tpublic setThumbnail(url: string | null): this {\n\t\t// Data assertions\n\t\timageURLPredicate.parse(url);\n\n\t\tthis.data.thumbnail = url ? { url } : undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the timestamp of this embed\n\t *\n\t * @param timestamp The timestamp or date\n\t */\n\tpublic setTimestamp(timestamp: number | Date | null = Date.now()): this {\n\t\t// Data assertions\n\t\ttimestampPredicate.parse(timestamp);\n\n\t\tthis.data.timestamp = timestamp ? new Date(timestamp).toISOString() : undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the title of this embed\n\t *\n\t * @param title The title\n\t */\n\tpublic setTitle(title: string | null): this {\n\t\t// Data assertions\n\t\ttitlePredicate.parse(title);\n\n\t\tthis.data.title = title ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the URL of this embed\n\t *\n\t * @param url The URL\n\t */\n\tpublic setURL(url: string | null): this {\n\t\t// Data assertions\n\t\turlPredicate.parse(url);\n\n\t\tthis.data.url = url ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Transforms the embed to a plain object\n\t */\n\tpublic toJSON(): APIEmbed {\n\t\treturn { ...this.data };\n\t}\n}\n", "import type { URL } from 'url';\nimport type { Snowflake } from 'discord-api-types/globals';\n\n/**\n * Wraps the content inside a codeblock with no language\n *\n * @param content - The content to wrap\n */\nexport function codeBlock<C extends string>(content: C): `\\`\\`\\`\\n${C}\\`\\`\\``;\n\n/**\n * Wraps the content inside a codeblock with the specified language\n *\n * @param language - The language for the codeblock\n * @param content - The content to wrap\n */\nexport function codeBlock<L extends string, C extends string>(language: L, content: C): `\\`\\`\\`${L}\\n${C}\\`\\`\\``;\nexport function codeBlock(language: string, content?: string): string {\n\treturn typeof content === 'undefined' ? `\\`\\`\\`\\n${language}\\`\\`\\`` : `\\`\\`\\`${language}\\n${content}\\`\\`\\``;\n}\n\n/**\n * Wraps the content inside \\`backticks\\`, which formats it as inline code\n *\n * @param content - The content to wrap\n */\nexport function inlineCode<C extends string>(content: C): `\\`${C}\\`` {\n\treturn `\\`${content}\\``;\n}\n\n/**\n * Formats the content into italic text\n *\n * @param content - The content to wrap\n */\nexport function italic<C extends string>(content: C): `_${C}_` {\n\treturn `_${content}_`;\n}\n\n/**\n * Formats the content into bold text\n *\n * @param content - The content to wrap\n */\nexport function bold<C extends string>(content: C): `**${C}**` {\n\treturn `**${content}**`;\n}\n\n/**\n * Formats the content into underscored text\n *\n * @param content - The content to wrap\n */\nexport function underscore<C extends string>(content: C): `__${C}__` {\n\treturn `__${content}__`;\n}\n\n/**\n * Formats the content into strike-through text\n *\n * @param content - The content to wrap\n */\nexport function strikethrough<C extends string>(content: C): `~~${C}~~` {\n\treturn `~~${content}~~`;\n}\n\n/**\n * Formats the content into a quote. This needs to be at the start of the line for Discord to format it\n *\n * @param content - The content to wrap\n */\nexport function quote<C extends string>(content: C): `> ${C}` {\n\treturn `> ${content}`;\n}\n\n/**\n * Formats the content into a block quote. This needs to be at the start of the line for Discord to format it\n *\n * @param content - The content to wrap\n */\nexport function blockQuote<C extends string>(content: C): `>>> ${C}` {\n\treturn `>>> ${content}`;\n}\n\n/**\n * Wraps the URL into `<>`, which stops it from embedding\n *\n * @param url - The URL to wrap\n */\nexport function hideLinkEmbed<C extends string>(url: C): `<${C}>`;\n\n/**\n * Wraps the URL into `<>`, which stops it from embedding\n *\n * @param url - The URL to wrap\n */\nexport function hideLinkEmbed(url: URL): `<${string}>`;\nexport function hideLinkEmbed(url: string | URL) {\n\t// eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n\treturn `<${url}>`;\n}\n\n/**\n * Formats the content and the URL into a masked URL\n *\n * @param content - The content to display\n * @param url - The URL the content links to\n */\nexport function hyperlink<C extends string>(content: C, url: URL): `[${C}](${string})`;\n\n/**\n * Formats the content and the URL into a masked URL\n *\n * @param content - The content to display\n * @param url - The URL the content links to\n */\nexport function hyperlink<C extends string, U extends string>(content: C, url: U): `[${C}](${U})`;\n\n/**\n * Formats the content and the URL into a masked URL\n *\n * @param content - The content to display\n * @param url - The URL the content links to\n * @param title - The title shown when hovering on the masked link\n */\nexport function hyperlink<C extends string, T extends string>(\n\tcontent: C,\n\turl: URL,\n\ttitle: T,\n): `[${C}](${string} \"${T}\")`;\n\n/**\n * Formats the content and the URL into a masked URL\n *\n * @param content - The content to display\n * @param url - The URL the content links to\n * @param title - The title shown when hovering on the masked link\n */\nexport function hyperlink<C extends string, U extends string, T extends string>(\n\tcontent: C,\n\turl: U,\n\ttitle: T,\n): `[${C}](${U} \"${T}\")`;\nexport function hyperlink(content: string, url: string | URL, title?: string) {\n\t// eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n\treturn title ? `[${content}](${url} \"${title}\")` : `[${content}](${url})`;\n}\n\n/**\n * Wraps the content inside spoiler (hidden text)\n *\n * @param content - The content to wrap\n */\nexport function spoiler<C extends string>(content: C): `||${C}||` {\n\treturn `||${content}||`;\n}\n\n/**\n * Formats a user ID into a user mention\n *\n * @param userId - The user ID to format\n */\nexport function userMention<C extends Snowflake>(userId: C): `<@${C}>` {\n\treturn `<@${userId}>`;\n}\n\n/**\n * Formats a channel ID into a channel mention\n *\n * @param channelId - The channel ID to format\n */\nexport function channelMention<C extends Snowflake>(channelId: C): `<#${C}>` {\n\treturn `<#${channelId}>`;\n}\n\n/**\n * Formats a role ID into a role mention\n *\n * @param roleId - The role ID to format\n */\nexport function roleMention<C extends Snowflake>(roleId: C): `<@&${C}>` {\n\treturn `<@&${roleId}>`;\n}\n\n/**\n * Formats an emoji ID into a fully qualified emoji identifier\n *\n * @param emojiId - The emoji ID to format\n */\nexport function formatEmoji<C extends Snowflake>(emojiId: C, animated?: false): `<:_:${C}>`;\n\n/**\n * Formats an emoji ID into a fully qualified emoji identifier\n *\n * @param emojiId - The emoji ID to format\n * @param animated - Whether the emoji is animated or not. Defaults to `false`\n */\nexport function formatEmoji<C extends Snowflake>(emojiId: C, animated?: true): `<a:_:${C}>`;\n\n/**\n * Formats an emoji ID into a fully qualified emoji identifier\n *\n * @param emojiId - The emoji ID to format\n * @param animated - Whether the emoji is animated or not. Defaults to `false`\n */\nexport function formatEmoji<C extends Snowflake>(emojiId: C, animated = false): `<a:_:${C}>` | `<:_:${C}>` {\n\treturn `<${animated ? 'a' : ''}:_:${emojiId}>`;\n}\n\n/**\n * Formats a date into a short date-time string\n *\n * @param date - The date to format, defaults to the current time\n */\nexport function time(date?: Date): `<t:${bigint}>`;\n\n/**\n * Formats a date given a format style\n *\n * @param date - The date to format\n * @param style - The style to use\n */\nexport function time<S extends TimestampStylesString>(date: Date, style: S): `<t:${bigint}:${S}>`;\n\n/**\n * Formats the given timestamp into a short date-time string\n *\n * @param seconds - The time to format, represents an UNIX timestamp in seconds\n */\nexport function time<C extends number>(seconds: C): `<t:${C}>`;\n\n/**\n * Formats the given timestamp into a short date-time string\n *\n * @param seconds - The time to format, represents an UNIX timestamp in seconds\n * @param style - The style to use\n */\nexport function time<C extends number, S extends TimestampStylesString>(seconds: C, style: S): `<t:${C}:${S}>`;\nexport function time(timeOrSeconds?: number | Date, style?: TimestampStylesString): string {\n\tif (typeof timeOrSeconds !== 'number') {\n\t\ttimeOrSeconds = Math.floor((timeOrSeconds?.getTime() ?? Date.now()) / 1000);\n\t}\n\n\treturn typeof style === 'string' ? `<t:${timeOrSeconds}:${style}>` : `<t:${timeOrSeconds}>`;\n}\n\n/**\n * The [message formatting timestamp styles](https://discord.com/developers/docs/reference#message-formatting-timestamp-styles) supported by Discord\n */\nexport const TimestampStyles = {\n\t/**\n\t * Short time format, consisting of hours and minutes, e.g. 16:20\n\t */\n\tShortTime: 't',\n\n\t/**\n\t * Long time format, consisting of hours, minutes, and seconds, e.g. 16:20:30\n\t */\n\tLongTime: 'T',\n\n\t/**\n\t * Short date format, consisting of day, month, and year, e.g. 20/04/2021\n\t */\n\tShortDate: 'd',\n\n\t/**\n\t * Long date format, consisting of day, month, and year, e.g. 20 April 2021\n\t */\n\tLongDate: 'D',\n\n\t/**\n\t * Short date-time format, consisting of short date and short time formats, e.g. 20 April 2021 16:20\n\t */\n\tShortDateTime: 'f',\n\n\t/**\n\t * Long date-time format, consisting of long date and short time formats, e.g. Tuesday, 20 April 2021 16:20\n\t */\n\tLongDateTime: 'F',\n\n\t/**\n\t * Relative time format, consisting of a relative duration format, e.g. 2 months ago\n\t */\n\tRelativeTime: 'R',\n} as const;\n\n/**\n * The possible values, see {@link TimestampStyles} for more information\n */\nexport type TimestampStylesString = typeof TimestampStyles[keyof typeof TimestampStyles];\n\n/**\n * An enum with all the available faces from Discord's native slash commands\n */\nexport enum Faces {\n\t/**\n\t * ¯\\\\_(ツ)\\\\_/¯\n\t */\n\tShrug = '¯\\\\_(ツ)\\\\_/¯',\n\n\t/**\n\t * (╯°□°）╯︵ ┻━┻\n\t */\n\tTableflip = '(╯°□°）╯︵ ┻━┻',\n\n\t/**\n\t * ┬─┬ ノ( ゜-゜ノ)\n\t */\n\tUnflip = '┬─┬ ノ( ゜-゜ノ)',\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { APIMessageComponentEmoji, ButtonStyle } from 'discord-api-types/v10';\nimport { SelectMenuOptionBuilder } from './selectMenu/SelectMenuOption';\nimport { isValidationEnabled } from '../util/validation';\n\nexport const customIdValidator = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(100)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const emojiValidator = s\n\t.object({\n\t\tid: s.string,\n\t\tname: s.string,\n\t\tanimated: s.boolean,\n\t})\n\t.partial.strict.setValidationEnabled(isValidationEnabled);\n\nexport const disabledValidator = s.boolean;\n\nexport const buttonLabelValidator = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(80)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const buttonStyleValidator = s.nativeEnum(ButtonStyle);\n\nexport const placeholderValidator = s.string.lengthLessThanOrEqual(150).setValidationEnabled(isValidationEnabled);\nexport const minMaxValidator = s.number.int\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(25)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const labelValueDescriptionValidator = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(100)\n\t.setValidationEnabled(isValidationEnabled);\nexport const optionValidator = s\n\t.union(\n\t\ts.object({\n\t\t\tlabel: labelValueDescriptionValidator,\n\t\t\tvalue: labelValueDescriptionValidator,\n\t\t\tdescription: labelValueDescriptionValidator.optional,\n\t\t\temoji: emojiValidator.optional,\n\t\t\tdefault: s.boolean.optional,\n\t\t}),\n\t\ts.instance(SelectMenuOptionBuilder),\n\t)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const optionsValidator = optionValidator.array\n\t.lengthGreaterThanOrEqual(0)\n\t.setValidationEnabled(isValidationEnabled);\nexport const optionsLengthValidator = s.number.int\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(25)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredSelectMenuParameters(options: SelectMenuOptionBuilder[], customId?: string) {\n\tcustomIdValidator.parse(customId);\n\toptionsValidator.parse(options);\n}\n\nexport const defaultValidator = s.boolean;\n\nexport function validateRequiredSelectMenuOptionParameters(label?: string, value?: string) {\n\tlabelValueDescriptionValidator.parse(label);\n\tlabelValueDescriptionValidator.parse(value);\n}\n\nexport const urlValidator = s.string\n\t.url({\n\t\tallowedProtocols: ['http:', 'https:', 'discord:'],\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredButtonParameters(\n\tstyle?: ButtonStyle,\n\tlabel?: string,\n\temoji?: APIMessageComponentEmoji,\n\tcustomId?: string,\n\turl?: string,\n) {\n\tif (url && customId) {\n\t\tthrow new RangeError('URL and custom id are mutually exclusive');\n\t}\n\n\tif (!label && !emoji) {\n\t\tthrow new RangeError('Buttons must have a label and/or an emoji');\n\t}\n\n\tif (style === ButtonStyle.Link) {\n\t\tif (!url) {\n\t\t\tthrow new RangeError('Link buttons must have a url');\n\t\t}\n\t} else if (url) {\n\t\tthrow new RangeError('Non-link buttons cannot have a url');\n\t}\n}\n", "import type { APIMessageComponentEmoji, APISelectMenuOption } from 'discord-api-types/v10';\n\nimport {\n\tdefaultValidator,\n\temojiValidator,\n\tlabelValueDescriptionValidator,\n\tvalidateRequiredSelectMenuOptionParameters,\n} from '../Assertions';\n\n/**\n * Represents a option within a select menu component\n */\nexport class SelectMenuOptionBuilder {\n\tpublic constructor(public data: Partial<APISelectMenuOption> = {}) {}\n\n\t/**\n\t * Sets the label of this option\n\t *\n\t * @param label - The label to show on this option\n\t */\n\tpublic setLabel(label: string) {\n\t\tthis.data.label = labelValueDescriptionValidator.parse(label);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the value of this option\n\t *\n\t * @param value - The value of this option\n\t */\n\tpublic setValue(value: string) {\n\t\tthis.data.value = labelValueDescriptionValidator.parse(value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description of this option\n\t *\n\t * @param description - The description of this option\n\t */\n\tpublic setDescription(description: string) {\n\t\tthis.data.description = labelValueDescriptionValidator.parse(description);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this option is selected by default\n\t *\n\t * @param isDefault - Whether this option is selected by default\n\t */\n\tpublic setDefault(isDefault = true) {\n\t\tthis.data.default = defaultValidator.parse(isDefault);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the emoji to display on this option\n\t *\n\t * @param emoji - The emoji to display on this option\n\t */\n\tpublic setEmoji(emoji: APIMessageComponentEmoji) {\n\t\tthis.data.emoji = emojiValidator.parse(emoji);\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APISelectMenuOption {\n\t\tvalidateRequiredSelectMenuOptionParameters(this.data.label, this.data.value);\n\t\t// eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APISelectMenuOption;\n\t}\n}\n", "import {\n\ttype APIActionRowComponent,\n\tComponentType,\n\tAPIMessageActionRowComponent,\n\tAPIModalActionRowComponent,\n\tAPIActionRowComponentTypes,\n} from 'discord-api-types/v10';\nimport { ComponentBuilder } from './Component';\nimport { createComponentBuilder } from './Components';\nimport type { ButtonBuilder } from './button/Button';\nimport type { SelectMenuBuilder } from './selectMenu/SelectMenu';\nimport type { TextInputBuilder } from './textInput/TextInput';\nimport { normalizeArray, type RestOrArray } from '../util/normalizeArray';\n\nexport type MessageComponentBuilder =\n\t| MessageActionRowComponentBuilder\n\t| ActionRowBuilder<MessageActionRowComponentBuilder>;\nexport type ModalComponentBuilder = ModalActionRowComponentBuilder | ActionRowBuilder<ModalActionRowComponentBuilder>;\nexport type MessageActionRowComponentBuilder = ButtonBuilder | SelectMenuBuilder;\nexport type ModalActionRowComponentBuilder = TextInputBuilder;\nexport type AnyComponentBuilder = MessageActionRowComponentBuilder | ModalActionRowComponentBuilder;\n\n/**\n * Represents an action row component\n */\nexport class ActionRowBuilder<T extends AnyComponentBuilder> extends ComponentBuilder<\n\tAPIActionRowComponent<APIMessageActionRowComponent | APIModalActionRowComponent>\n> {\n\t/**\n\t * The components within this action row\n\t */\n\tpublic readonly components: T[];\n\n\tpublic constructor({ components, ...data }: Partial<APIActionRowComponent<APIActionRowComponentTypes>> = {}) {\n\t\tsuper({ type: ComponentType.ActionRow, ...data });\n\t\tthis.components = (components?.map((c) => createComponentBuilder(c)) ?? []) as T[];\n\t}\n\n\t/**\n\t * Adds components to this action row.\n\t *\n\t * @param components - The components to add to this action row.\n\t */\n\tpublic addComponents(...components: RestOrArray<T>) {\n\t\tthis.components.push(...normalizeArray(components));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the components in this action row\n\t *\n\t * @param components - The components to set this row to\n\t */\n\tpublic setComponents(...components: RestOrArray<T>) {\n\t\tthis.components.splice(0, this.components.length, ...normalizeArray(components));\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIActionRowComponent<ReturnType<T['toJSON']>> {\n\t\t// eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n\t\treturn {\n\t\t\t...this.data,\n\t\t\tcomponents: this.components.map((component) => component.toJSON()),\n\t\t} as APIActionRowComponent<ReturnType<T['toJSON']>>;\n\t}\n}\n", "import type {\n\tAPIActionRowComponent,\n\tAPIActionRowComponentTypes,\n\tAPIBaseComponent,\n\tComponentType,\n} from 'discord-api-types/v10';\nimport type { JSONEncodable } from '../util/jsonEncodable';\n\nexport type AnyAPIActionRowComponent = APIActionRowComponentTypes | APIActionRowComponent<APIActionRowComponentTypes>;\n\n/**\n * Represents a discord component\n */\nexport abstract class ComponentBuilder<\n\tDataType extends Partial<APIBaseComponent<ComponentType>> = APIBaseComponent<ComponentType>,\n> implements JSONEncodable<AnyAPIActionRowComponent>\n{\n\t/**\n\t * The API data associated with this component\n\t */\n\tpublic readonly data: Partial<DataType>;\n\n\tpublic abstract toJSON(): AnyAPIActionRowComponent;\n\n\tpublic constructor(data: Partial<DataType>) {\n\t\tthis.data = data;\n\t}\n}\n", "import { APIMessageComponent, APIModalComponent, ComponentType } from 'discord-api-types/v10';\nimport {\n\tActionRowBuilder,\n\ttype AnyComponentBuilder,\n\ttype MessageComponentBuilder,\n\ttype ModalComponentBuilder,\n} from './ActionRow';\nimport { ComponentBuilder } from './Component';\nimport { ButtonBuilder } from './button/Button';\nimport { SelectMenuBuilder } from './selectMenu/SelectMenu';\nimport { TextInputBuilder } from './textInput/TextInput';\n\nexport interface MappedComponentTypes {\n\t[ComponentType.ActionRow]: ActionRowBuilder<AnyComponentBuilder>;\n\t[ComponentType.Button]: ButtonBuilder;\n\t[ComponentType.SelectMenu]: SelectMenuBuilder;\n\t[ComponentType.TextInput]: TextInputBuilder;\n}\n\n/**\n * Factory for creating components from API data\n *\n * @param data - The api data to transform to a component class\n */\nexport function createComponentBuilder<T extends keyof MappedComponentTypes>(\n\tdata: (APIMessageComponent | APIModalComponent) & { type: T },\n): MappedComponentTypes[T];\nexport function createComponentBuilder<C extends MessageComponentBuilder | ModalComponentBuilder>(data: C): C;\nexport function createComponentBuilder(\n\tdata: APIMessageComponent | APIModalComponent | MessageComponentBuilder,\n): ComponentBuilder {\n\tif (data instanceof ComponentBuilder) {\n\t\treturn data;\n\t}\n\n\tswitch (data.type) {\n\t\tcase ComponentType.ActionRow:\n\t\t\treturn new ActionRowBuilder(data);\n\t\tcase ComponentType.Button:\n\t\t\treturn new ButtonBuilder(data);\n\t\tcase ComponentType.SelectMenu:\n\t\t\treturn new SelectMenuBuilder(data);\n\t\tcase ComponentType.TextInput:\n\t\t\treturn new TextInputBuilder(data);\n\t\tdefault:\n\t\t\t// @ts-expect-error\n\t\t\t// eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n\t\t\tthrow new Error(`Cannot properly serialize component type: ${data.type}`);\n\t}\n}\n", "import {\n\tComponentType,\n\tButtonStyle,\n\ttype APIMessageComponentEmoji,\n\ttype APIButtonComponent,\n\ttype APIButtonComponentWithURL,\n\ttype APIButtonComponentWithCustomId,\n} from 'discord-api-types/v10';\nimport {\n\tbuttonLabelValidator,\n\tbuttonStyleValidator,\n\tcustomIdValidator,\n\tdisabledValidator,\n\temojiValidator,\n\turlValidator,\n\tvalidateRequiredButtonParameters,\n} from '../Assertions';\nimport { ComponentBuilder } from '../Component';\n\n/**\n * Represents a button component\n */\nexport class ButtonBuilder extends ComponentBuilder<APIButtonComponent> {\n\tpublic constructor(data?: Partial<APIButtonComponent>) {\n\t\tsuper({ type: ComponentType.Button, ...data });\n\t}\n\n\t/**\n\t * Sets the style of this button\n\t *\n\t * @param style - The style of the button\n\t */\n\tpublic setStyle(style: ButtonStyle) {\n\t\tthis.data.style = buttonStyleValidator.parse(style);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the URL for this button\n\t *\n\t * @param url - The URL to open when this button is clicked\n\t */\n\tpublic setURL(url: string) {\n\t\t(this.data as APIButtonComponentWithURL).url = urlValidator.parse(url);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the custom id for this button\n\t *\n\t * @param customId - The custom id to use for this button\n\t */\n\tpublic setCustomId(customId: string) {\n\t\t(this.data as APIButtonComponentWithCustomId).custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the emoji to display on this button\n\t *\n\t * @param emoji - The emoji to display on this button\n\t */\n\tpublic setEmoji(emoji: APIMessageComponentEmoji) {\n\t\tthis.data.emoji = emojiValidator.parse(emoji);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this button is disabled\n\t *\n\t * @param disabled - Whether to disable this button\n\t */\n\tpublic setDisabled(disabled = true) {\n\t\tthis.data.disabled = disabledValidator.parse(disabled);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the label for this button\n\t *\n\t * @param label - The label to display on this button\n\t */\n\tpublic setLabel(label: string) {\n\t\tthis.data.label = buttonLabelValidator.parse(label);\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIButtonComponent {\n\t\tvalidateRequiredButtonParameters(\n\t\t\tthis.data.style,\n\t\t\tthis.data.label,\n\t\t\tthis.data.emoji,\n\t\t\t(this.data as APIButtonComponentWithCustomId).custom_id,\n\t\t\t(this.data as APIButtonComponentWithURL).url,\n\t\t);\n\t\t// eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APIButtonComponent;\n\t}\n}\n", "import { APISelectMenuOption, ComponentType, type APISelectMenuComponent } from 'discord-api-types/v10';\nimport { SelectMenuOptionBuilder } from './SelectMenuOption';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray';\nimport {\n\tcustomIdValidator,\n\tdisabledValidator,\n\tminMaxValidator,\n\toptionsLengthValidator,\n\toptionValidator,\n\tplaceholderValidator,\n\tvalidateRequiredSelectMenuParameters,\n} from '../Assertions';\nimport { ComponentBuilder } from '../Component';\n\n/**\n * Represents a select menu component\n */\nexport class SelectMenuBuilder extends ComponentBuilder<APISelectMenuComponent> {\n\t/**\n\t * The options within this select menu\n\t */\n\tpublic readonly options: SelectMenuOptionBuilder[];\n\n\tpublic constructor(data?: Partial<APISelectMenuComponent>) {\n\t\tconst { options, ...initData } = data ?? {};\n\t\tsuper({ type: ComponentType.SelectMenu, ...initData });\n\t\tthis.options = options?.map((o) => new SelectMenuOptionBuilder(o)) ?? [];\n\t}\n\n\t/**\n\t * Sets the placeholder for this select menu\n\t *\n\t * @param placeholder - The placeholder to use for this select menu\n\t */\n\tpublic setPlaceholder(placeholder: string) {\n\t\tthis.data.placeholder = placeholderValidator.parse(placeholder);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the minimum values that must be selected in the select menu\n\t *\n\t * @param minValues - The minimum values that must be selected\n\t */\n\tpublic setMinValues(minValues: number) {\n\t\tthis.data.min_values = minMaxValidator.parse(minValues);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the maximum values that must be selected in the select menu\n\t *\n\t * @param maxValues - The maximum values that must be selected\n\t */\n\tpublic setMaxValues(maxValues: number) {\n\t\tthis.data.max_values = minMaxValidator.parse(maxValues);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the custom id for this select menu\n\t *\n\t * @param customId - The custom id to use for this select menu\n\t */\n\tpublic setCustomId(customId: string) {\n\t\tthis.data.custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this select menu is disabled\n\t *\n\t * @param disabled - Whether this select menu is disabled\n\t */\n\tpublic setDisabled(disabled = true) {\n\t\tthis.data.disabled = disabledValidator.parse(disabled);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds options to this select menu\n\t *\n\t * @param options - The options to add to this select menu\n\t * @returns\n\t */\n\tpublic addOptions(...options: RestOrArray<SelectMenuOptionBuilder | APISelectMenuOption>) {\n\t\toptions = normalizeArray(options);\n\t\toptionsLengthValidator.parse(this.options.length + options.length);\n\t\tthis.options.push(\n\t\t\t...options.map((option) =>\n\t\t\t\toption instanceof SelectMenuOptionBuilder\n\t\t\t\t\t? option\n\t\t\t\t\t: new SelectMenuOptionBuilder(optionValidator.parse<APISelectMenuOption>(option)),\n\t\t\t),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the options on this select menu\n\t *\n\t * @param options - The options to set on this select menu\n\t */\n\tpublic setOptions(...options: RestOrArray<SelectMenuOptionBuilder | APISelectMenuOption>) {\n\t\toptions = normalizeArray(options);\n\t\toptionsLengthValidator.parse(options.length);\n\t\tthis.options.splice(\n\t\t\t0,\n\t\t\tthis.options.length,\n\t\t\t...options.map((option) =>\n\t\t\t\toption instanceof SelectMenuOptionBuilder\n\t\t\t\t\t? option\n\t\t\t\t\t: new SelectMenuOptionBuilder(optionValidator.parse<APISelectMenuOption>(option)),\n\t\t\t),\n\t\t);\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APISelectMenuComponent {\n\t\tvalidateRequiredSelectMenuParameters(this.options, this.data.custom_id);\n\t\t// eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n\t\treturn {\n\t\t\t...this.data,\n\t\t\toptions: this.options.map((o) => o.toJSON()),\n\t\t} as APISelectMenuComponent;\n\t}\n}\n", "import { ComponentType, type TextInputStyle, type APITextInputComponent } from 'discord-api-types/v10';\nimport isEqual from 'fast-deep-equal';\nimport {\n\tmaxLengthValidator,\n\tminLengthValidator,\n\tplaceholderValidator,\n\trequiredValidator,\n\tvalueValidator,\n\tvalidateRequiredParameters,\n\tlabelValidator,\n\ttextInputStyleValidator,\n} from './Assertions';\nimport { isJSONEncodable, type JSONEncodable } from '../../util/jsonEncodable';\nimport { customIdValidator } from '../Assertions';\nimport { ComponentBuilder } from '../Component';\n\nexport class TextInputBuilder extends ComponentBuilder<APITextInputComponent> {\n\tpublic constructor(data?: APITextInputComponent & { type?: ComponentType.TextInput }) {\n\t\tsuper({ type: ComponentType.TextInput, ...data });\n\t}\n\n\t/**\n\t * Sets the custom id for this text input\n\t *\n\t * @param customId - The custom id of this text input\n\t */\n\tpublic setCustomId(customId: string) {\n\t\tthis.data.custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the label for this text input\n\t *\n\t * @param label - The label for this text input\n\t */\n\tpublic setLabel(label: string) {\n\t\tthis.data.label = labelValidator.parse(label);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the style for this text input\n\t *\n\t * @param style - The style for this text input\n\t */\n\tpublic setStyle(style: TextInputStyle) {\n\t\tthis.data.style = textInputStyleValidator.parse(style);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the minimum length of text for this text input\n\t *\n\t * @param minLength - The minimum length of text for this text input\n\t */\n\tpublic setMinLength(minLength: number) {\n\t\tthis.data.min_length = minLengthValidator.parse(minLength);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the maximum length of text for this text input\n\t *\n\t * @param maxLength - The maximum length of text for this text input\n\t */\n\tpublic setMaxLength(maxLength: number) {\n\t\tthis.data.max_length = maxLengthValidator.parse(maxLength);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the placeholder of this text input\n\t *\n\t * @param placeholder - The placeholder of this text input\n\t */\n\tpublic setPlaceholder(placeholder: string) {\n\t\tthis.data.placeholder = placeholderValidator.parse(placeholder);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the value of this text input\n\t *\n\t * @param value - The value for this text input\n\t */\n\tpublic setValue(value: string) {\n\t\tthis.data.value = valueValidator.parse(value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this text input is required\n\t *\n\t * @param required - Whether this text input is required\n\t */\n\tpublic setRequired(required = true) {\n\t\tthis.data.required = requiredValidator.parse(required);\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APITextInputComponent {\n\t\tvalidateRequiredParameters(this.data.custom_id, this.data.style, this.data.label);\n\t\t// eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APITextInputComponent;\n\t}\n\n\tpublic equals(other: JSONEncodable<APITextInputComponent> | APITextInputComponent): boolean {\n\t\tif (isJSONEncodable(other)) {\n\t\t\treturn isEqual(other.toJSON(), this.data);\n\t\t}\n\n\t\treturn isEqual(other, this.data);\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { TextInputStyle } from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../../util/validation';\nimport { customIdValidator } from '../Assertions';\n\nexport const textInputStyleValidator = s.nativeEnum(TextInputStyle);\nexport const minLengthValidator = s.number.int\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(4000)\n\t.setValidationEnabled(isValidationEnabled);\nexport const maxLengthValidator = s.number.int\n\t.greaterThanOrEqual(1)\n\t.lessThanOrEqual(4000)\n\t.setValidationEnabled(isValidationEnabled);\nexport const requiredValidator = s.boolean;\nexport const valueValidator = s.string.lengthLessThanOrEqual(4000).setValidationEnabled(isValidationEnabled);\nexport const placeholderValidator = s.string.lengthLessThanOrEqual(100).setValidationEnabled(isValidationEnabled);\nexport const labelValidator = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(45)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredParameters(customId?: string, style?: TextInputStyle, label?: string) {\n\tcustomIdValidator.parse(customId);\n\ttextInputStyleValidator.parse(style);\n\tlabelValidator.parse(label);\n}\n", "export interface JSONEncodable<T> {\n\t/**\n\t * Transforms this object to its JSON format\n\t */\n\ttoJSON: () => T;\n}\n\n/**\n * Indicates if an object is encodable or not.\n * @param maybeEncodable - The object to check against\n */\nexport function isJSONEncodable(maybeEncodable: unknown): maybeEncodable is JSONEncodable<unknown> {\n\treturn maybeEncodable !== null && typeof maybeEncodable === 'object' && 'toJSON' in maybeEncodable;\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ActionRowBuilder, type ModalActionRowComponentBuilder } from '../../components/ActionRow';\nimport { customIdValidator } from '../../components/Assertions';\nimport { isValidationEnabled } from '../../util/validation';\n\nexport const titleValidator = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(45)\n\t.setValidationEnabled(isValidationEnabled);\nexport const componentsValidator = s\n\t.instance(ActionRowBuilder)\n\t.array.lengthGreaterThanOrEqual(1)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredParameters(\n\tcustomId?: string,\n\ttitle?: string,\n\tcomponents?: ActionRowBuilder<ModalActionRowComponentBuilder>[],\n) {\n\tcustomIdValidator.parse(customId);\n\ttitleValidator.parse(title);\n\tcomponentsValidator.parse(components);\n}\n", "import type {\n\tAPIActionRowComponent,\n\tAPIModalActionRowComponent,\n\tAPIModalInteractionResponseCallbackData,\n} from 'discord-api-types/v10';\nimport { titleValidator, validateRequiredParameters } from './Assertions';\nimport { ActionRowBuilder, type ModalActionRowComponentBuilder } from '../../components/ActionRow';\nimport { customIdValidator } from '../../components/Assertions';\nimport { createComponentBuilder } from '../../components/Components';\nimport type { JSONEncodable } from '../../util/jsonEncodable';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray';\n\nexport class ModalBuilder implements JSONEncodable<APIModalInteractionResponseCallbackData> {\n\tpublic readonly data: Partial<APIModalInteractionResponseCallbackData>;\n\tpublic readonly components: ActionRowBuilder<ModalActionRowComponentBuilder>[] = [];\n\n\tpublic constructor({ components, ...data }: Partial<APIModalInteractionResponseCallbackData> = {}) {\n\t\tthis.data = { ...data };\n\t\tthis.components = (components?.map((c) => createComponentBuilder(c)) ??\n\t\t\t[]) as ActionRowBuilder<ModalActionRowComponentBuilder>[];\n\t}\n\n\t/**\n\t * Sets the title of the modal\n\t *\n\t * @param title - The title of the modal\n\t */\n\tpublic setTitle(title: string) {\n\t\tthis.data.title = titleValidator.parse(title);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the custom id of the modal\n\t *\n\t * @param customId - The custom id of this modal\n\t */\n\tpublic setCustomId(customId: string) {\n\t\tthis.data.custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds components to this modal\n\t *\n\t * @param components - The components to add to this modal\n\t */\n\tpublic addComponents(\n\t\t...components: RestOrArray<\n\t\t\tActionRowBuilder<ModalActionRowComponentBuilder> | APIActionRowComponent<APIModalActionRowComponent>\n\t\t>\n\t) {\n\t\tthis.components.push(\n\t\t\t...normalizeArray(components).map((component) =>\n\t\t\t\tcomponent instanceof ActionRowBuilder\n\t\t\t\t\t? component\n\t\t\t\t\t: new ActionRowBuilder<ModalActionRowComponentBuilder>(component),\n\t\t\t),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the components in this modal\n\t *\n\t * @param components - The components to set this modal to\n\t */\n\tpublic setComponents(...components: RestOrArray<ActionRowBuilder<ModalActionRowComponentBuilder>>) {\n\t\tthis.components.splice(0, this.components.length, ...normalizeArray(components));\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIModalInteractionResponseCallbackData {\n\t\tvalidateRequiredParameters(this.data.custom_id, this.data.title, this.components);\n\t\t// eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n\t\treturn {\n\t\t\t...this.data,\n\t\t\tcomponents: this.components.map((component) => component.toJSON()),\n\t\t} as APIModalInteractionResponseCallbackData;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { type APIApplicationCommandOptionChoice, Locale, LocalizationMap } from 'discord-api-types/v10';\nimport type { ToAPIApplicationCommandOptions } from './SlashCommandBuilder';\nimport type { SlashCommandSubcommandBuilder, SlashCommandSubcommandGroupBuilder } from './SlashCommandSubcommands';\nimport type { ApplicationCommandOptionBase } from './mixins/ApplicationCommandOptionBase';\nimport { isValidationEnabled } from '../../util/validation';\n\nconst namePredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(32)\n\t.regex(/^[\\p{Ll}\\p{Lm}\\p{Lo}\\p{N}\\p{sc=Devanagari}\\p{sc=Thai}_-]+$/u)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateName(name: unknown): asserts name is string {\n\tnamePredicate.parse(name);\n}\n\nconst descriptionPredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(100)\n\t.setValidationEnabled(isValidationEnabled);\nconst localePredicate = s.nativeEnum(Locale);\n\nexport function validateDescription(description: unknown): asserts description is string {\n\tdescriptionPredicate.parse(description);\n}\n\nconst maxArrayLengthPredicate = s.unknown.array.lengthLessThanOrEqual(25).setValidationEnabled(isValidationEnabled);\nexport function validateLocale(locale: unknown) {\n\treturn localePredicate.parse(locale);\n}\n\nexport function validateMaxOptionsLength(options: unknown): asserts options is ToAPIApplicationCommandOptions[] {\n\tmaxArrayLengthPredicate.parse(options);\n}\n\nexport function validateRequiredParameters(\n\tname: string,\n\tdescription: string,\n\toptions: ToAPIApplicationCommandOptions[],\n) {\n\t// Assert name matches all conditions\n\tvalidateName(name);\n\n\t// Assert description conditions\n\tvalidateDescription(description);\n\n\t// Assert options conditions\n\tvalidateMaxOptionsLength(options);\n}\n\nconst booleanPredicate = s.boolean;\n\nexport function validateDefaultPermission(value: unknown): asserts value is boolean {\n\tbooleanPredicate.parse(value);\n}\n\nexport function validateRequired(required: unknown): asserts required is boolean {\n\tbooleanPredicate.parse(required);\n}\n\nconst choicesLengthPredicate = s.number.lessThanOrEqual(25).setValidationEnabled(isValidationEnabled);\n\nexport function validateChoicesLength(amountAdding: number, choices?: APIApplicationCommandOptionChoice[]): void {\n\tchoicesLengthPredicate.parse((choices?.length ?? 0) + amountAdding);\n}\n\nexport function assertReturnOfBuilder<\n\tT extends ApplicationCommandOptionBase | SlashCommandSubcommandBuilder | SlashCommandSubcommandGroupBuilder,\n>(input: unknown, ExpectedInstanceOf: new () => T): asserts input is T {\n\ts.instance(ExpectedInstanceOf).parse(input);\n}\n\nexport const localizationMapPredicate = s\n\t.object<LocalizationMap>(Object.fromEntries(Object.values(Locale).map((locale) => [locale, s.string.nullish])))\n\t.strict.nullish.setValidationEnabled(isValidationEnabled);\n\nexport function validateLocalizationMap(value: unknown): asserts value is LocalizationMap {\n\tlocalizationMapPredicate.parse(value);\n}\n\nconst dmPermissionPredicate = s.boolean.nullish;\n\nexport function validateDMPermission(value: unknown): asserts value is boolean | null | undefined {\n\tdmPermissionPredicate.parse(value);\n}\n\nconst memberPermissionPredicate = s.union(\n\ts.bigint.transform((value) => value.toString()),\n\ts.number.safeInt.transform((value) => value.toString()),\n\ts.string.regex(/^\\d+$/),\n).nullish;\n\nexport function validateDefaultMemberPermissions(permissions: unknown) {\n\treturn memberPermissionPredicate.parse(permissions);\n}\n", "import type {\n\tAPIApplicationCommandOption,\n\tLocalizationMap,\n\tPermissions,\n\tRESTPostAPIApplicationCommandsJSONBody,\n} from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport {\n\tassertReturnOfBuilder,\n\tvalidateDefaultMemberPermissions,\n\tvalidateDefaultPermission,\n\tvalidateLocalizationMap,\n\tvalidateDMPermission,\n\tvalidateMaxOptionsLength,\n\tvalidateRequiredParameters,\n} from './Assertions';\nimport { SlashCommandSubcommandBuilder, SlashCommandSubcommandGroupBuilder } from './SlashCommandSubcommands';\nimport { SharedNameAndDescription } from './mixins/NameAndDescription';\nimport { SharedSlashCommandOptions } from './mixins/SharedSlashCommandOptions';\n\n@mix(SharedSlashCommandOptions, SharedNameAndDescription)\nexport class SlashCommandBuilder {\n\t/**\n\t * The name of this slash command\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The localized names for this command\n\t */\n\tpublic readonly name_localizations?: LocalizationMap;\n\n\t/**\n\t * The description of this slash command\n\t */\n\tpublic readonly description: string = undefined!;\n\n\t/**\n\t * The localized descriptions for this command\n\t */\n\tpublic readonly description_localizations?: LocalizationMap;\n\n\t/**\n\t * The options of this slash command\n\t */\n\tpublic readonly options: ToAPIApplicationCommandOptions[] = [];\n\n\t/**\n\t * Whether the command is enabled by default when the app is added to a guild\n\t *\n\t * @deprecated This property is deprecated and will be removed in the future.\n\t * You should use `setDefaultMemberPermissions` or `setDMPermission` instead.\n\t */\n\tpublic readonly default_permission: boolean | undefined = undefined;\n\n\t/**\n\t * Set of permissions represented as a bit set for the command\n\t */\n\tpublic readonly default_member_permissions: Permissions | null | undefined = undefined;\n\n\t/**\n\t * Indicates whether the command is available in DMs with the application, only for globally-scoped commands.\n\t * By default, commands are visible.\n\t */\n\tpublic readonly dm_permission: boolean | undefined = undefined;\n\n\t/**\n\t * Returns the final data that should be sent to Discord.\n\t *\n\t * **Note:** Calling this function will validate required properties based on their conditions.\n\t */\n\tpublic toJSON(): RESTPostAPIApplicationCommandsJSONBody {\n\t\tvalidateRequiredParameters(this.name, this.description, this.options);\n\n\t\tvalidateLocalizationMap(this.name_localizations);\n\t\tvalidateLocalizationMap(this.description_localizations);\n\n\t\treturn {\n\t\t\t...this,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t};\n\t}\n\n\t/**\n\t * Sets whether the command is enabled by default when the application is added to a guild.\n\t *\n\t * **Note**: If set to `false`, you will have to later `PUT` the permissions for this command.\n\t *\n\t * @param value - Whether or not to enable this command by default\n\t *\n\t * @see https://discord.com/developers/docs/interactions/application-commands#permissions\n\t * @deprecated Use `setDefaultMemberPermissions` or `setDMPermission` instead.\n\t */\n\tpublic setDefaultPermission(value: boolean) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDefaultPermission(value);\n\n\t\tReflect.set(this, 'default_permission', value);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the default permissions a member should have in order to run the command.\n\t *\n\t * **Note:** You can set this to `'0'` to disable the command by default.\n\t *\n\t * @param permissions - The permissions bit field to set\n\t *\n\t * @see https://discord.com/developers/docs/interactions/application-commands#permissions\n\t */\n\tpublic setDefaultMemberPermissions(permissions: Permissions | bigint | number | null | undefined) {\n\t\t// Assert the value and parse it\n\t\tconst permissionValue = validateDefaultMemberPermissions(permissions);\n\n\t\tReflect.set(this, 'default_member_permissions', permissionValue);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets if the command is available in DMs with the application, only for globally-scoped commands.\n\t * By default, commands are visible.\n\t *\n\t * @param enabled - If the command should be enabled in DMs\n\t *\n\t * @see https://discord.com/developers/docs/interactions/application-commands#permissions\n\t */\n\tpublic setDMPermission(enabled: boolean | null | undefined) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDMPermission(enabled);\n\n\t\tReflect.set(this, 'dm_permission', enabled);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a new subcommand group to this command\n\t *\n\t * @param input - A function that returns a subcommand group builder, or an already built builder\n\t */\n\tpublic addSubcommandGroup(\n\t\tinput:\n\t\t\t| SlashCommandSubcommandGroupBuilder\n\t\t\t| ((subcommandGroup: SlashCommandSubcommandGroupBuilder) => SlashCommandSubcommandGroupBuilder),\n\t): SlashCommandSubcommandsOnlyBuilder {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\tconst result = typeof input === 'function' ? input(new SlashCommandSubcommandGroupBuilder()) : input;\n\n\t\tassertReturnOfBuilder(result, SlashCommandSubcommandGroupBuilder);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a new subcommand to this command\n\t *\n\t * @param input - A function that returns a subcommand builder, or an already built builder\n\t */\n\tpublic addSubcommand(\n\t\tinput:\n\t\t\t| SlashCommandSubcommandBuilder\n\t\t\t| ((subcommandGroup: SlashCommandSubcommandBuilder) => SlashCommandSubcommandBuilder),\n\t): SlashCommandSubcommandsOnlyBuilder {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\tconst result = typeof input === 'function' ? input(new SlashCommandSubcommandBuilder()) : input;\n\n\t\tassertReturnOfBuilder(result, SlashCommandSubcommandBuilder);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this;\n\t}\n}\n\nexport interface SlashCommandBuilder extends SharedNameAndDescription, SharedSlashCommandOptions {}\n\nexport interface SlashCommandSubcommandsOnlyBuilder\n\textends SharedNameAndDescription,\n\t\tPick<SlashCommandBuilder, 'toJSON' | 'addSubcommand' | 'addSubcommandGroup'> {}\n\nexport interface SlashCommandOptionsOnlyBuilder\n\textends SharedNameAndDescription,\n\t\tSharedSlashCommandOptions,\n\t\tPick<SlashCommandBuilder, 'toJSON'> {}\n\nexport interface ToAPIApplicationCommandOptions {\n\ttoJSON: () => APIApplicationCommandOption;\n}\n", "import {\n\tAPIApplicationCommandSubcommandGroupOption,\n\tAPIApplicationCommandSubcommandOption,\n\tApplicationCommandOptionType,\n} from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { assertReturnOfBuilder, validateMaxOptionsLength, validateRequiredParameters } from './Assertions';\nimport type { ToAPIApplicationCommandOptions } from './SlashCommandBuilder';\nimport type { ApplicationCommandOptionBase } from './mixins/ApplicationCommandOptionBase';\nimport { SharedNameAndDescription } from './mixins/NameAndDescription';\nimport { SharedSlashCommandOptions } from './mixins/SharedSlashCommandOptions';\n\n/**\n * Represents a folder for subcommands\n *\n * For more information, go to https://discord.com/developers/docs/interactions/application-commands#subcommands-and-subcommand-groups\n */\n@mix(SharedNameAndDescription)\nexport class SlashCommandSubcommandGroupBuilder implements ToAPIApplicationCommandOptions {\n\t/**\n\t * The name of this subcommand group\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The description of this subcommand group\n\t */\n\tpublic readonly description: string = undefined!;\n\n\t/**\n\t * The subcommands part of this subcommand group\n\t */\n\tpublic readonly options: SlashCommandSubcommandBuilder[] = [];\n\n\t/**\n\t * Adds a new subcommand to this group\n\t *\n\t * @param input - A function that returns a subcommand builder, or an already built builder\n\t */\n\tpublic addSubcommand(\n\t\tinput:\n\t\t\t| SlashCommandSubcommandBuilder\n\t\t\t| ((subcommandGroup: SlashCommandSubcommandBuilder) => SlashCommandSubcommandBuilder),\n\t) {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\tconst result = typeof input === 'function' ? input(new SlashCommandSubcommandBuilder()) : input;\n\n\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\tassertReturnOfBuilder(result, SlashCommandSubcommandBuilder);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIApplicationCommandSubcommandGroupOption {\n\t\tvalidateRequiredParameters(this.name, this.description, this.options);\n\n\t\treturn {\n\t\t\ttype: ApplicationCommandOptionType.SubcommandGroup,\n\t\t\tname: this.name,\n\t\t\tname_localizations: this.name_localizations,\n\t\t\tdescription: this.description,\n\t\t\tdescription_localizations: this.description_localizations,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t};\n\t}\n}\n\nexport interface SlashCommandSubcommandGroupBuilder extends SharedNameAndDescription {}\n\n/**\n * Represents a subcommand\n *\n * For more information, go to https://discord.com/developers/docs/interactions/application-commands#subcommands-and-subcommand-groups\n */\n@mix(SharedNameAndDescription, SharedSlashCommandOptions)\nexport class SlashCommandSubcommandBuilder implements ToAPIApplicationCommandOptions {\n\t/**\n\t * The name of this subcommand\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The description of this subcommand\n\t */\n\tpublic readonly description: string = undefined!;\n\n\t/**\n\t * The options of this subcommand\n\t */\n\tpublic readonly options: ApplicationCommandOptionBase[] = [];\n\n\tpublic toJSON(): APIApplicationCommandSubcommandOption {\n\t\tvalidateRequiredParameters(this.name, this.description, this.options);\n\n\t\treturn {\n\t\t\ttype: ApplicationCommandOptionType.Subcommand,\n\t\t\tname: this.name,\n\t\t\tname_localizations: this.name_localizations,\n\t\t\tdescription: this.description,\n\t\t\tdescription_localizations: this.description_localizations,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t};\n\t}\n}\n\nexport interface SlashCommandSubcommandBuilder extends SharedNameAndDescription, SharedSlashCommandOptions<false> {}\n", "import type { LocaleString, LocalizationMap } from 'discord-api-types/v10';\nimport { validateDescription, validateLocale, validateName } from '../Assertions';\n\nexport class SharedNameAndDescription {\n\tpublic readonly name!: string;\n\tpublic readonly name_localizations?: LocalizationMap;\n\tpublic readonly description!: string;\n\tpublic readonly description_localizations?: LocalizationMap;\n\n\t/**\n\t * Sets the name\n\t *\n\t * @param name - The name\n\t */\n\tpublic setName(name: string): this {\n\t\t// Assert the name matches the conditions\n\t\tvalidateName(name);\n\n\t\tReflect.set(this, 'name', name);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description\n\t *\n\t * @param description - The description\n\t */\n\tpublic setDescription(description: string) {\n\t\t// Assert the description matches the conditions\n\t\tvalidateDescription(description);\n\n\t\tReflect.set(this, 'description', description);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets a name localization\n\t *\n\t * @param locale - The locale to set a description for\n\t * @param localizedName - The localized description for the given locale\n\t */\n\tpublic setNameLocalization(locale: LocaleString, localizedName: string | null) {\n\t\tif (!this.name_localizations) {\n\t\t\tReflect.set(this, 'name_localizations', {});\n\t\t}\n\n\t\tconst parsedLocale = validateLocale(locale);\n\n\t\tif (localizedName === null) {\n\t\t\tthis.name_localizations![parsedLocale] = null;\n\t\t\treturn this;\n\t\t}\n\n\t\tvalidateName(localizedName);\n\n\t\tthis.name_localizations![parsedLocale] = localizedName;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the name localizations\n\t *\n\t * @param localizedNames - The dictionary of localized descriptions to set\n\t */\n\tpublic setNameLocalizations(localizedNames: LocalizationMap | null) {\n\t\tif (localizedNames === null) {\n\t\t\tReflect.set(this, 'name_localizations', null);\n\t\t\treturn this;\n\t\t}\n\n\t\tReflect.set(this, 'name_localizations', {});\n\n\t\tObject.entries(localizedNames).forEach((args) =>\n\t\t\tthis.setNameLocalization(...(args as [LocaleString, string | null])),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets a description localization\n\t *\n\t * @param locale - The locale to set a description for\n\t * @param localizedDescription - The localized description for the given locale\n\t */\n\tpublic setDescriptionLocalization(locale: LocaleString, localizedDescription: string | null) {\n\t\tif (!this.description_localizations) {\n\t\t\tReflect.set(this, 'description_localizations', {});\n\t\t}\n\n\t\tconst parsedLocale = validateLocale(locale);\n\n\t\tif (localizedDescription === null) {\n\t\t\tthis.description_localizations![parsedLocale] = null;\n\t\t\treturn this;\n\t\t}\n\n\t\tvalidateDescription(localizedDescription);\n\n\t\tthis.description_localizations![parsedLocale] = localizedDescription;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description localizations\n\t *\n\t * @param localizedDescriptions - The dictionary of localized descriptions to set\n\t */\n\tpublic setDescriptionLocalizations(localizedDescriptions: LocalizationMap | null) {\n\t\tif (localizedDescriptions === null) {\n\t\t\tReflect.set(this, 'description_localizations', null);\n\t\t\treturn this;\n\t\t}\n\n\t\tReflect.set(this, 'description_localizations', {});\n\t\tObject.entries(localizedDescriptions).forEach((args) =>\n\t\t\tthis.setDescriptionLocalization(...(args as [LocaleString, string | null])),\n\t\t);\n\t\treturn this;\n\t}\n}\n", "import { APIApplicationCommandAttachmentOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\n\nexport class SlashCommandAttachmentOption extends ApplicationCommandOptionBase {\n\tpublic override readonly type = ApplicationCommandOptionType.Attachment as const;\n\n\tpublic toJSON(): APIApplicationCommandAttachmentOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import type { APIApplicationCommandBasicOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { SharedNameAndDescription } from './NameAndDescription';\nimport { validateRequiredParameters, validateRequired, validateLocalizationMap } from '../Assertions';\n\nexport abstract class ApplicationCommandOptionBase extends SharedNameAndDescription {\n\tpublic abstract readonly type: ApplicationCommandOptionType;\n\n\tpublic readonly required: boolean = false;\n\n\t/**\n\t * Marks the option as required\n\t *\n\t * @param required - If this option should be required\n\t */\n\tpublic setRequired(required: boolean) {\n\t\t// Assert that you actually passed a boolean\n\t\tvalidateRequired(required);\n\n\t\tReflect.set(this, 'required', required);\n\n\t\treturn this;\n\t}\n\n\tpublic abstract toJSON(): APIApplicationCommandBasicOption;\n\n\tprotected runRequiredValidations() {\n\t\tvalidateRequiredParameters(this.name, this.description, []);\n\n\t\t// Validate localizations\n\t\tvalidateLocalizationMap(this.name_localizations);\n\t\tvalidateLocalizationMap(this.description_localizations);\n\n\t\t// Assert that you actually passed a boolean\n\t\tvalidateRequired(this.required);\n\t}\n}\n", "import { APIApplicationCommandBooleanOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\n\nexport class SlashCommandBooleanOption extends ApplicationCommandOptionBase {\n\tpublic readonly type = ApplicationCommandOptionType.Boolean as const;\n\n\tpublic toJSON(): APIApplicationCommandBooleanOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { APIApplicationCommandChannelOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\nimport { ApplicationCommandOptionChannelTypesMixin } from '../mixins/ApplicationCommandOptionChannelTypesMixin';\n\n@mix(ApplicationCommandOptionChannelTypesMixin)\nexport class SlashCommandChannelOption extends ApplicationCommandOptionBase {\n\tpublic override readonly type = ApplicationCommandOptionType.Channel as const;\n\n\tpublic toJSON(): APIApplicationCommandChannelOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n\nexport interface SlashCommandChannelOption extends ApplicationCommandOptionChannelTypesMixin {}\n", "import { s } from '@sapphire/shapeshift';\nimport { ChannelType } from 'discord-api-types/v10';\n\n// Only allow valid channel types to be used. (This can't be dynamic because const enums are erased at runtime)\nconst allowedChannelTypes = [\n\tChannelType.GuildText,\n\tChannelType.GuildVoice,\n\tChannelType.GuildCategory,\n\tChannelType.GuildNews,\n\tChannelType.GuildNewsThread,\n\tChannelType.GuildPublicThread,\n\tChannelType.GuildPrivateThread,\n\tChannelType.GuildStageVoice,\n] as const;\n\nexport type ApplicationCommandOptionAllowedChannelTypes = typeof allowedChannelTypes[number];\n\nconst channelTypesPredicate = s.array(s.union(...allowedChannelTypes.map((type) => s.literal(type))));\n\nexport class ApplicationCommandOptionChannelTypesMixin {\n\tpublic readonly channel_types?: ApplicationCommandOptionAllowedChannelTypes[];\n\n\t/**\n\t * Adds channel types to this option\n\t *\n\t * @param channelTypes - The channel types to add\n\t */\n\tpublic addChannelTypes(...channelTypes: ApplicationCommandOptionAllowedChannelTypes[]) {\n\t\tif (this.channel_types === undefined) {\n\t\t\tReflect.set(this, 'channel_types', []);\n\t\t}\n\n\t\tthis.channel_types!.push(...channelTypesPredicate.parse(channelTypes));\n\n\t\treturn this;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { APIApplicationCommandIntegerOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandNumericOptionMinMaxValueMixin } from '../mixins/ApplicationCommandNumericOptionMinMaxValueMixin';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\nimport { ApplicationCommandOptionWithChoicesAndAutocompleteMixin } from '../mixins/ApplicationCommandOptionWithChoicesAndAutocompleteMixin';\n\nconst numberValidator = s.number.int;\n\n@mix(ApplicationCommandNumericOptionMinMaxValueMixin, ApplicationCommandOptionWithChoicesAndAutocompleteMixin)\nexport class SlashCommandIntegerOption\n\textends ApplicationCommandOptionBase\n\timplements ApplicationCommandNumericOptionMinMaxValueMixin\n{\n\tpublic readonly type = ApplicationCommandOptionType.Integer as const;\n\n\tpublic setMaxValue(max: number): this {\n\t\tnumberValidator.parse(max);\n\n\t\tReflect.set(this, 'max_value', max);\n\n\t\treturn this;\n\t}\n\n\tpublic setMinValue(min: number): this {\n\t\tnumberValidator.parse(min);\n\n\t\tReflect.set(this, 'min_value', min);\n\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIApplicationCommandIntegerOption {\n\t\tthis.runRequiredValidations();\n\n\t\tif (this.autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\treturn { ...this };\n\t}\n}\n\nexport interface SlashCommandIntegerOption\n\textends ApplicationCommandNumericOptionMinMaxValueMixin,\n\t\tApplicationCommandOptionWithChoicesAndAutocompleteMixin<number> {}\n", "export abstract class ApplicationCommandNumericOptionMinMaxValueMixin {\n\tpublic readonly max_value?: number;\n\tpublic readonly min_value?: number;\n\n\t/**\n\t * Sets the maximum number value of this option\n\t *\n\t * @param max - The maximum value this option can be\n\t */\n\tpublic abstract setMaxValue(max: number): this;\n\n\t/**\n\t * Sets the minimum number value of this option\n\t *\n\t * @param min - The minimum value this option can be\n\t */\n\tpublic abstract setMinValue(min: number): this;\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { APIApplicationCommandOptionChoice, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { localizationMapPredicate, validateChoicesLength } from '../Assertions';\n\nconst stringPredicate = s.string.lengthGreaterThanOrEqual(1).lengthLessThanOrEqual(100);\nconst numberPredicate = s.number.greaterThan(-Infinity).lessThan(Infinity);\nconst choicesPredicate = s.object({\n\tname: stringPredicate,\n\tname_localizations: localizationMapPredicate,\n\tvalue: s.union(stringPredicate, numberPredicate),\n}).array;\nconst booleanPredicate = s.boolean;\n\nexport class ApplicationCommandOptionWithChoicesAndAutocompleteMixin<T extends string | number> {\n\tpublic readonly choices?: APIApplicationCommandOptionChoice<T>[];\n\tpublic readonly autocomplete?: boolean;\n\n\t// Since this is present and this is a mixin, this is needed\n\tpublic readonly type!: ApplicationCommandOptionType;\n\n\t/**\n\t * Adds multiple choices for this option\n\t *\n\t * @param choices - The choices to add\n\t */\n\tpublic addChoices(...choices: APIApplicationCommandOptionChoice<T>[]): this {\n\t\tif (choices.length > 0 && this.autocomplete) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\tchoicesPredicate.parse(choices);\n\n\t\tif (this.choices === undefined) {\n\t\t\tReflect.set(this, 'choices', []);\n\t\t}\n\n\t\tvalidateChoicesLength(choices.length, this.choices);\n\n\t\tfor (const { name, name_localizations, value } of choices) {\n\t\t\t// Validate the value\n\t\t\tif (this.type === ApplicationCommandOptionType.String) {\n\t\t\t\tstringPredicate.parse(value);\n\t\t\t} else {\n\t\t\t\tnumberPredicate.parse(value);\n\t\t\t}\n\n\t\t\tthis.choices!.push({ name, name_localizations, value });\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tpublic setChoices<Input extends APIApplicationCommandOptionChoice<T>[]>(...choices: Input): this {\n\t\tif (choices.length > 0 && this.autocomplete) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\tchoicesPredicate.parse(choices);\n\n\t\tReflect.set(this, 'choices', []);\n\t\tthis.addChoices(...choices);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Marks the option as autocompletable\n\t * @param autocomplete - If this option should be autocompletable\n\t */\n\tpublic setAutocomplete(autocomplete: boolean): this {\n\t\t// Assert that you actually passed a boolean\n\t\tbooleanPredicate.parse(autocomplete);\n\n\t\tif (autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\tReflect.set(this, 'autocomplete', autocomplete);\n\n\t\treturn this;\n\t}\n}\n", "import { APIApplicationCommandMentionableOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\n\nexport class SlashCommandMentionableOption extends ApplicationCommandOptionBase {\n\tpublic readonly type = ApplicationCommandOptionType.Mentionable as const;\n\n\tpublic toJSON(): APIApplicationCommandMentionableOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { APIApplicationCommandNumberOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandNumericOptionMinMaxValueMixin } from '../mixins/ApplicationCommandNumericOptionMinMaxValueMixin';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\nimport { ApplicationCommandOptionWithChoicesAndAutocompleteMixin } from '../mixins/ApplicationCommandOptionWithChoicesAndAutocompleteMixin';\n\nconst numberValidator = s.number;\n\n@mix(ApplicationCommandNumericOptionMinMaxValueMixin, ApplicationCommandOptionWithChoicesAndAutocompleteMixin)\nexport class SlashCommandNumberOption\n\textends ApplicationCommandOptionBase\n\timplements ApplicationCommandNumericOptionMinMaxValueMixin\n{\n\tpublic readonly type = ApplicationCommandOptionType.Number as const;\n\n\tpublic setMaxValue(max: number): this {\n\t\tnumberValidator.parse(max);\n\n\t\tReflect.set(this, 'max_value', max);\n\n\t\treturn this;\n\t}\n\n\tpublic setMinValue(min: number): this {\n\t\tnumberValidator.parse(min);\n\n\t\tReflect.set(this, 'min_value', min);\n\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIApplicationCommandNumberOption {\n\t\tthis.runRequiredValidations();\n\n\t\tif (this.autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\treturn { ...this };\n\t}\n}\n\nexport interface SlashCommandNumberOption\n\textends ApplicationCommandNumericOptionMinMaxValueMixin,\n\t\tApplicationCommandOptionWithChoicesAndAutocompleteMixin<number> {}\n", "import { APIApplicationCommandRoleOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\n\nexport class SlashCommandRoleOption extends ApplicationCommandOptionBase {\n\tpublic override readonly type = ApplicationCommandOptionType.Role as const;\n\n\tpublic toJSON(): APIApplicationCommandRoleOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { APIApplicationCommandStringOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\nimport { ApplicationCommandOptionWithChoicesAndAutocompleteMixin } from '../mixins/ApplicationCommandOptionWithChoicesAndAutocompleteMixin';\n\nconst minLengthValidator = s.number.greaterThanOrEqual(0).lessThanOrEqual(6000);\nconst maxLengthValidator = s.number.greaterThanOrEqual(1).lessThanOrEqual(6000);\n\n@mix(ApplicationCommandOptionWithChoicesAndAutocompleteMixin)\nexport class SlashCommandStringOption extends ApplicationCommandOptionBase {\n\tpublic readonly type = ApplicationCommandOptionType.String as const;\n\tpublic readonly max_length?: number;\n\tpublic readonly min_length?: number;\n\n\t/**\n\t * Sets the maximum length of this string option.\n\t *\n\t * @param max - The maximum length this option can be\n\t */\n\tpublic setMaxLength(max: number): this {\n\t\tmaxLengthValidator.parse(max);\n\n\t\tReflect.set(this, 'max_length', max);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the minimum length of this string option.\n\t *\n\t * @param min - The minimum length this option can be\n\t */\n\tpublic setMinLength(min: number): this {\n\t\tminLengthValidator.parse(min);\n\n\t\tReflect.set(this, 'min_length', min);\n\n\t\treturn this;\n\t}\n\n\tpublic toJSON(): APIApplicationCommandStringOption {\n\t\tthis.runRequiredValidations();\n\n\t\tif (this.autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\treturn { ...this };\n\t}\n}\n\nexport interface SlashCommandStringOption extends ApplicationCommandOptionWithChoicesAndAutocompleteMixin<string> {}\n", "import { APIApplicationCommandUserOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase';\n\nexport class SlashCommandUserOption extends ApplicationCommandOptionBase {\n\tpublic readonly type = ApplicationCommandOptionType.User as const;\n\n\tpublic toJSON(): APIApplicationCommandUserOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import type { ApplicationCommandOptionBase } from './ApplicationCommandOptionBase';\nimport { assertReturnOfBuilder, validateMaxOptionsLength } from '../Assertions';\nimport type { ToAPIApplicationCommandOptions } from '../SlashCommandBuilder';\nimport { SlashCommandAttachmentOption } from '../options/attachment';\nimport { SlashCommandBooleanOption } from '../options/boolean';\nimport { SlashCommandChannelOption } from '../options/channel';\nimport { SlashCommandIntegerOption } from '../options/integer';\nimport { SlashCommandMentionableOption } from '../options/mentionable';\nimport { SlashCommandNumberOption } from '../options/number';\nimport { SlashCommandRoleOption } from '../options/role';\nimport { SlashCommandStringOption } from '../options/string';\nimport { SlashCommandUserOption } from '../options/user';\n\nexport class SharedSlashCommandOptions<ShouldOmitSubcommandFunctions = true> {\n\tpublic readonly options!: ToAPIApplicationCommandOptions[];\n\n\t/**\n\t * Adds a boolean option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addBooleanOption(\n\t\tinput: SlashCommandBooleanOption | ((builder: SlashCommandBooleanOption) => SlashCommandBooleanOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandBooleanOption);\n\t}\n\n\t/**\n\t * Adds a user option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addUserOption(input: SlashCommandUserOption | ((builder: SlashCommandUserOption) => SlashCommandUserOption)) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandUserOption);\n\t}\n\n\t/**\n\t * Adds a channel option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addChannelOption(\n\t\tinput: SlashCommandChannelOption | ((builder: SlashCommandChannelOption) => SlashCommandChannelOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandChannelOption);\n\t}\n\n\t/**\n\t * Adds a role option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addRoleOption(input: SlashCommandRoleOption | ((builder: SlashCommandRoleOption) => SlashCommandRoleOption)) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandRoleOption);\n\t}\n\n\t/**\n\t * Adds an attachment option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addAttachmentOption(\n\t\tinput: SlashCommandAttachmentOption | ((builder: SlashCommandAttachmentOption) => SlashCommandAttachmentOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandAttachmentOption);\n\t}\n\n\t/**\n\t * Adds a mentionable option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addMentionableOption(\n\t\tinput: SlashCommandMentionableOption | ((builder: SlashCommandMentionableOption) => SlashCommandMentionableOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandMentionableOption);\n\t}\n\n\t/**\n\t * Adds a string option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addStringOption(\n\t\tinput:\n\t\t\t| SlashCommandStringOption\n\t\t\t| Omit<SlashCommandStringOption, 'setAutocomplete'>\n\t\t\t| Omit<SlashCommandStringOption, 'addChoices'>\n\t\t\t| ((\n\t\t\t\t\tbuilder: SlashCommandStringOption,\n\t\t\t  ) =>\n\t\t\t\t\t| SlashCommandStringOption\n\t\t\t\t\t| Omit<SlashCommandStringOption, 'setAutocomplete'>\n\t\t\t\t\t| Omit<SlashCommandStringOption, 'addChoices'>),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandStringOption);\n\t}\n\n\t/**\n\t * Adds an integer option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addIntegerOption(\n\t\tinput:\n\t\t\t| SlashCommandIntegerOption\n\t\t\t| Omit<SlashCommandIntegerOption, 'setAutocomplete'>\n\t\t\t| Omit<SlashCommandIntegerOption, 'addChoices'>\n\t\t\t| ((\n\t\t\t\t\tbuilder: SlashCommandIntegerOption,\n\t\t\t  ) =>\n\t\t\t\t\t| SlashCommandIntegerOption\n\t\t\t\t\t| Omit<SlashCommandIntegerOption, 'setAutocomplete'>\n\t\t\t\t\t| Omit<SlashCommandIntegerOption, 'addChoices'>),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandIntegerOption);\n\t}\n\n\t/**\n\t * Adds a number option\n\t *\n\t * @param input - A function that returns an option builder, or an already built builder\n\t */\n\tpublic addNumberOption(\n\t\tinput:\n\t\t\t| SlashCommandNumberOption\n\t\t\t| Omit<SlashCommandNumberOption, 'setAutocomplete'>\n\t\t\t| Omit<SlashCommandNumberOption, 'addChoices'>\n\t\t\t| ((\n\t\t\t\t\tbuilder: SlashCommandNumberOption,\n\t\t\t  ) =>\n\t\t\t\t\t| SlashCommandNumberOption\n\t\t\t\t\t| Omit<SlashCommandNumberOption, 'setAutocomplete'>\n\t\t\t\t\t| Omit<SlashCommandNumberOption, 'addChoices'>),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandNumberOption);\n\t}\n\n\tprivate _sharedAddOptionMethod<T extends ApplicationCommandOptionBase>(\n\t\tinput:\n\t\t\t| T\n\t\t\t| Omit<T, 'setAutocomplete'>\n\t\t\t| Omit<T, 'addChoices'>\n\t\t\t| ((builder: T) => T | Omit<T, 'setAutocomplete'> | Omit<T, 'addChoices'>),\n\t\tInstance: new () => T,\n\t): ShouldOmitSubcommandFunctions extends true ? Omit<this, 'addSubcommand' | 'addSubcommandGroup'> : this {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\tconst result = typeof input === 'function' ? input(new Instance()) : input;\n\n\t\tassertReturnOfBuilder(result, Instance);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ApplicationCommandType } from 'discord-api-types/v10';\nimport type { ContextMenuCommandType } from './ContextMenuCommandBuilder';\nimport { isValidationEnabled } from '../../util/validation';\n\nconst namePredicate = s.string\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(32)\n\t.regex(/^( *[\\p{L}\\p{N}\\p{sc=Devanagari}\\p{sc=Thai}_-]+ *)+$/u)\n\t.setValidationEnabled(isValidationEnabled);\nconst typePredicate = s\n\t.union(s.literal(ApplicationCommandType.User), s.literal(ApplicationCommandType.Message))\n\t.setValidationEnabled(isValidationEnabled);\nconst booleanPredicate = s.boolean;\n\nexport function validateDefaultPermission(value: unknown): asserts value is boolean {\n\tbooleanPredicate.parse(value);\n}\n\nexport function validateName(name: unknown): asserts name is string {\n\tnamePredicate.parse(name);\n}\n\nexport function validateType(type: unknown): asserts type is ContextMenuCommandType {\n\ttypePredicate.parse(type);\n}\n\nexport function validateRequiredParameters(name: string, type: number) {\n\t// Assert name matches all conditions\n\tvalidateName(name);\n\n\t// Assert type is valid\n\tvalidateType(type);\n}\n\nconst dmPermissionPredicate = s.boolean.nullish;\n\nexport function validateDMPermission(value: unknown): asserts value is boolean | null | undefined {\n\tdmPermissionPredicate.parse(value);\n}\n\nconst memberPermissionPredicate = s.union(\n\ts.bigint.transform((value) => value.toString()),\n\ts.number.safeInt.transform((value) => value.toString()),\n\ts.string.regex(/^\\d+$/),\n).nullish;\n\nexport function validateDefaultMemberPermissions(permissions: unknown) {\n\treturn memberPermissionPredicate.parse(permissions);\n}\n", "import type {\n\tApplicationCommandType,\n\tLocaleString,\n\tLocalizationMap,\n\tPermissions,\n\tRESTPostAPIApplicationCommandsJSONBody,\n} from 'discord-api-types/v10';\nimport {\n\tvalidateRequiredParameters,\n\tvalidateName,\n\tvalidateType,\n\tvalidateDefaultPermission,\n\tvalidateDefaultMemberPermissions,\n\tvalidateDMPermission,\n} from './Assertions';\nimport { validateLocale, validateLocalizationMap } from '../slashCommands/Assertions';\n\nexport class ContextMenuCommandBuilder {\n\t/**\n\t * The name of this context menu command\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The localized names for this command\n\t */\n\tpublic readonly name_localizations?: LocalizationMap;\n\n\t/**\n\t * The type of this context menu command\n\t */\n\tpublic readonly type: ContextMenuCommandType = undefined!;\n\n\t/**\n\t * Whether the command is enabled by default when the app is added to a guild\n\t *\n\t * @deprecated This property is deprecated and will be removed in the future.\n\t * You should use `setDefaultMemberPermissions` or `setDMPermission` instead.\n\t */\n\tpublic readonly default_permission: boolean | undefined = undefined;\n\n\t/**\n\t * Set of permissions represented as a bit set for the command\n\t */\n\tpublic readonly default_member_permissions: Permissions | null | undefined = undefined;\n\n\t/**\n\t * Indicates whether the command is available in DMs with the application, only for globally-scoped commands.\n\t * By default, commands are visible.\n\t */\n\tpublic readonly dm_permission: boolean | undefined = undefined;\n\n\t/**\n\t * Sets the name\n\t *\n\t * @param name - The name\n\t */\n\tpublic setName(name: string) {\n\t\t// Assert the name matches the conditions\n\t\tvalidateName(name);\n\n\t\tReflect.set(this, 'name', name);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the type\n\t *\n\t * @param type - The type\n\t */\n\tpublic setType(type: ContextMenuCommandType) {\n\t\t// Assert the type is valid\n\t\tvalidateType(type);\n\n\t\tReflect.set(this, 'type', type);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether the command is enabled by default when the application is added to a guild.\n\t *\n\t * **Note**: If set to `false`, you will have to later `PUT` the permissions for this command.\n\t *\n\t * @param value - Whether or not to enable this command by default\n\t *\n\t * @see https://discord.com/developers/docs/interactions/application-commands#permissions\n\t * @deprecated Use `setDefaultMemberPermissions` or `setDMPermission` instead.\n\t */\n\tpublic setDefaultPermission(value: boolean) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDefaultPermission(value);\n\n\t\tReflect.set(this, 'default_permission', value);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the default permissions a member should have in order to run the command.\n\t *\n\t * **Note:** You can set this to `'0'` to disable the command by default.\n\t *\n\t * @param permissions - The permissions bit field to set\n\t *\n\t * @see https://discord.com/developers/docs/interactions/application-commands#permissions\n\t */\n\tpublic setDefaultMemberPermissions(permissions: Permissions | bigint | number | null | undefined) {\n\t\t// Assert the value and parse it\n\t\tconst permissionValue = validateDefaultMemberPermissions(permissions);\n\n\t\tReflect.set(this, 'default_member_permissions', permissionValue);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets if the command is available in DMs with the application, only for globally-scoped commands.\n\t * By default, commands are visible.\n\t *\n\t * @param enabled - If the command should be enabled in DMs\n\t *\n\t * @see https://discord.com/developers/docs/interactions/application-commands#permissions\n\t */\n\tpublic setDMPermission(enabled: boolean | null | undefined) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDMPermission(enabled);\n\n\t\tReflect.set(this, 'dm_permission', enabled);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets a name localization\n\t *\n\t * @param locale - The locale to set a description for\n\t * @param localizedName - The localized description for the given locale\n\t */\n\tpublic setNameLocalization(locale: LocaleString, localizedName: string | null) {\n\t\tif (!this.name_localizations) {\n\t\t\tReflect.set(this, 'name_localizations', {});\n\t\t}\n\n\t\tconst parsedLocale = validateLocale(locale);\n\n\t\tif (localizedName === null) {\n\t\t\tthis.name_localizations![parsedLocale] = null;\n\t\t\treturn this;\n\t\t}\n\n\t\tvalidateName(localizedName);\n\n\t\tthis.name_localizations![parsedLocale] = localizedName;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the name localizations\n\t *\n\t * @param localizedNames - The dictionary of localized descriptions to set\n\t */\n\tpublic setNameLocalizations(localizedNames: LocalizationMap | null) {\n\t\tif (localizedNames === null) {\n\t\t\tReflect.set(this, 'name_localizations', null);\n\t\t\treturn this;\n\t\t}\n\n\t\tReflect.set(this, 'name_localizations', {});\n\n\t\tObject.entries(localizedNames).forEach((args) =>\n\t\t\tthis.setNameLocalization(...(args as [LocaleString, string | null])),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Returns the final data that should be sent to Discord.\n\t *\n\t * **Note:** Calling this function will validate required properties based on their conditions.\n\t */\n\tpublic toJSON(): RESTPostAPIApplicationCommandsJSONBody {\n\t\tvalidateRequiredParameters(this.name, this.type);\n\n\t\tvalidateLocalizationMap(this.name_localizations);\n\n\t\treturn { ...this };\n\t}\n}\n\nexport type ContextMenuCommandType = ApplicationCommandType.User | ApplicationCommandType.Message;\n", "export interface Equatable<T> {\n\t/**\n\t * Whether or not this is equal to another structure\n\t */\n\tequals: (other: T) => boolean;\n}\n\n/**\n * Indicates if an object is equatable or not.\n * @param maybeEquatable - The object to check against\n */\nexport function isEquatable(maybeEquatable: unknown): maybeEquatable is Equatable<unknown> {\n\treturn maybeEquatable !== null && typeof maybeEquatable === 'object' && 'equals' in maybeEquatable;\n}\n", "import type { APIEmbed } from 'discord-api-types/v10';\n\nexport function embedLength(data: APIEmbed) {\n\treturn (\n\t\t(data.title?.length ?? 0) +\n\t\t(data.description?.length ?? 0) +\n\t\t(data.fields?.reduce((prev, curr) => prev + curr.name.length + curr.value.length, 0) ?? 0) +\n\t\t(data.footer?.text.length ?? 0) +\n\t\t(data.author?.name.length ?? 0)\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI,WAAW;AAER,IAAM,mBAAmB,6BAAO,WAAW,MAAlB;AACzB,IAAM,oBAAoB,6BAAO,WAAW,OAAlB;AAC1B,IAAM,sBAAsB,6BAAM,UAAN;;;ADA5B,IAAM,qBAAqB,EAAE,OAClC,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AAEnC,IAAM,sBAAsB,EAAE,OACnC,yBAAyB,CAAC,EAC1B,sBAAsB,IAAI,EAC1B,qBAAqB,mBAAmB;AAEnC,IAAM,uBAAuB,EAAE,QAAQ;AAEvC,IAAM,sBAAsB,EACjC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACT,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,4BAA4B,oBAAoB,MAAM,qBAAqB,mBAAmB;AAEpG,IAAM,uBAAuB,EAAE,OAAO,gBAAgB,EAAE,EAAE,qBAAqB,mBAAmB;AAElG,6BAA6B,cAAsB,QAAgC;AACzF,uBAAqB,MAAO,SAAQ,UAAU,KAAK,YAAY;AAChE;AAFgB;AAIT,IAAM,sBAAsB,mBAAmB,SAAS,qBAAqB,mBAAmB;AAEhG,IAAM,oBAAoB,EAAE,OACjC,IAAI;AAAA,EACJ,kBAAkB,CAAC,SAAS,UAAU,aAAa;AACpD,CAAC,EACA,QAAQ,qBAAqB,mBAAmB;AAE3C,IAAM,eAAe,EAAE,OAC5B,IAAI;AAAA,EACJ,kBAAkB,CAAC,SAAS,QAAQ;AACrC,CAAC,EACA,QAAQ,qBAAqB,mBAAmB;AAE3C,IAAM,uBAAuB,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AACN,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,eAAe,EAAE,OAAO,IACnC,mBAAmB,CAAC,EACpB,gBAAgB,GAAG,EACnB,qBAAqB,mBAAmB;AACnC,IAAM,iBAAiB,EAAE,OAAO,IACrC,mBAAmB,CAAC,EACpB,gBAAgB,QAAQ,EACxB,GAAG,EAAE,MAAM,CAAC,cAAc,cAAc,YAAY,CAAC,CAAC,EACtD,SAAS,qBAAqB,mBAAmB;AAE5C,IAAM,uBAAuB,EAAE,OACpC,yBAAyB,CAAC,EAC1B,sBAAsB,IAAI,EAC1B,SAAS,qBAAqB,mBAAmB;AAE5C,IAAM,sBAAsB,EAAE,OACnC,yBAAyB,CAAC,EAC1B,sBAAsB,IAAI,EAC1B,SAAS,qBAAqB,mBAAmB;AAE5C,IAAM,uBAAuB,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AACV,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,qBAAqB,mBAAmB;AAEtG,IAAM,iBAAiB,mBAAmB,SAAS,qBAAqB,mBAAmB;;;AEnF3F,wBAA2B,KAA0B;AAC3D,MAAI,MAAM,QAAQ,IAAI,EAAE;AAAG,WAAO,IAAI;AACtC,SAAO;AACR;AAHgB;;;AC6CT,IAAM,eAAN,MAAmB;AAAA,EAGzB,AAAO,YAAY,OAAiB,CAAC,GAAG;AAFxC,wBAAgB;AAGf,SAAK,OAAO,EAAE,GAAG,KAAK;AACtB,QAAI,KAAK;AAAW,WAAK,KAAK,YAAY,IAAI,KAAK,KAAK,SAAS,EAAE,YAAY;AAAA,EAChF;AAAA,EAOA,AAAO,aAAa,QAA0C;AAC7D,aAAS,eAAe,MAAM;AAE9B,wBAAoB,OAAO,QAAQ,KAAK,KAAK,MAAM;AAGnD,8BAA0B,MAAM,MAAM;AAEtC,QAAI,KAAK,KAAK;AAAQ,WAAK,KAAK,OAAO,KAAK,GAAG,MAAM;AAAA;AAChD,WAAK,KAAK,SAAS;AACxB,WAAO;AAAA,EACR;AAAA,EASA,AAAO,aAAa,OAAe,gBAAwB,QAA+B;AAEzF,wBAAoB,OAAO,SAAS,aAAa,KAAK,KAAK,MAAM;AAGjE,8BAA0B,MAAM,MAAM;AACtC,QAAI,KAAK,KAAK;AAAQ,WAAK,KAAK,OAAO,OAAO,OAAO,aAAa,GAAG,MAAM;AAAA;AACtE,WAAK,KAAK,SAAS;AACxB,WAAO;AAAA,EACR;AAAA,EAMA,AAAO,aAAa,QAAoC;AACvD,SAAK,aAAa,GAAG,KAAK,KAAK,QAAQ,UAAU,GAAG,GAAG,eAAe,MAAM,CAAC;AAC7E,WAAO;AAAA,EACR;AAAA,EAQA,AAAO,UAAU,SAA0C;AAC1D,QAAI,YAAY,MAAM;AACrB,WAAK,KAAK,SAAS;AACnB,aAAO;AAAA,IACR;AAGA,yBAAqB,MAAM,OAAO;AAElC,SAAK,KAAK,SAAS,EAAE,MAAM,QAAQ,MAAM,KAAK,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AACrF,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAuC;AAEtD,mBAAe,MAAM,KAAK;AAE1B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,CAAC,KAAK,OAAO,QAAQ;AAC3B,WAAK,KAAK,QAAS,QAAO,MAAO,UAAS,KAAK;AAC/C,aAAO;AAAA,IACR;AACA,SAAK,KAAK,QAAQ,SAAS;AAC3B,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,eAAe,aAAkC;AAEvD,yBAAqB,MAAM,WAAW;AAEtC,SAAK,KAAK,cAAc,eAAe;AACvC,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,UAAU,SAA0C;AAC1D,QAAI,YAAY,MAAM;AACrB,WAAK,KAAK,SAAS;AACnB,aAAO;AAAA,IACR;AAGA,yBAAqB,MAAM,OAAO;AAElC,SAAK,KAAK,SAAS,EAAE,MAAM,QAAQ,MAAM,UAAU,QAAQ,QAAQ;AACnE,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,KAA0B;AAEzC,sBAAkB,MAAM,GAAG;AAE3B,SAAK,KAAK,QAAQ,MAAM,EAAE,IAAI,IAAI;AAClC,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,KAA0B;AAE7C,sBAAkB,MAAM,GAAG;AAE3B,SAAK,KAAK,YAAY,MAAM,EAAE,IAAI,IAAI;AACtC,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,YAAkC,KAAK,IAAI,GAAS;AAEvE,uBAAmB,MAAM,SAAS;AAElC,SAAK,KAAK,YAAY,YAAY,IAAI,KAAK,SAAS,EAAE,YAAY,IAAI;AACtE,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAA4B;AAE3C,mBAAe,MAAM,KAAK;AAE1B,SAAK,KAAK,QAAQ,SAAS;AAC3B,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,OAAO,KAA0B;AAEvC,iBAAa,MAAM,GAAG;AAEtB,SAAK,KAAK,MAAM,OAAO;AACvB,WAAO;AAAA,EACR;AAAA,EAKA,AAAO,SAAmB;AACzB,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACvB;AACD;AAhMa;;;AC5BN,mBAAmB,UAAkB,SAA0B;AACrE,SAAO,OAAO,YAAY,cAAc;AAAA,EAAW,mBAAmB,SAAS;AAAA,EAAa;AAC7F;AAFgB;AAST,oBAAsC,SAAwB;AACpE,SAAO,KAAK;AACb;AAFgB;AAST,gBAAkC,SAAsB;AAC9D,SAAO,IAAI;AACZ;AAFgB;AAST,cAAgC,SAAwB;AAC9D,SAAO,KAAK;AACb;AAFgB;AAST,oBAAsC,SAAwB;AACpE,SAAO,KAAK;AACb;AAFgB;AAST,uBAAyC,SAAwB;AACvE,SAAO,KAAK;AACb;AAFgB;AAST,eAAiC,SAAsB;AAC7D,SAAO,KAAK;AACb;AAFgB;AAST,oBAAsC,SAAwB;AACpE,SAAO,OAAO;AACf;AAFgB;AAiBT,uBAAuB,KAAmB;AAEhD,SAAO,IAAI;AACZ;AAHgB;AA8CT,mBAAmB,SAAiB,KAAmB,OAAgB;AAE7E,SAAO,QAAQ,IAAI,YAAY,QAAQ,YAAY,IAAI,YAAY;AACpE;AAHgB;AAUT,iBAAmC,SAAwB;AACjE,SAAO,KAAK;AACb;AAFgB;AAST,qBAA0C,QAAsB;AACtE,SAAO,KAAK;AACb;AAFgB;AAST,wBAA6C,WAAyB;AAC5E,SAAO,KAAK;AACb;AAFgB;AAST,qBAA0C,QAAuB;AACvE,SAAO,MAAM;AACd;AAFgB;AAyBT,qBAA0C,SAAY,WAAW,OAAmC;AAC1G,SAAO,IAAI,WAAW,MAAM,QAAQ;AACrC;AAFgB;AAiCT,cAAc,eAA+B,OAAuC;AAC1F,MAAI,OAAO,kBAAkB,UAAU;AACtC,oBAAgB,KAAK,MAAO,gBAAe,QAAQ,KAAK,KAAK,IAAI,KAAK,GAAI;AAAA,EAC3E;AAEA,SAAO,OAAO,UAAU,WAAW,MAAM,iBAAiB,WAAW,MAAM;AAC5E;AANgB;AAWT,IAAM,kBAAkB;AAAA,EAI9B,WAAW;AAAA,EAKX,UAAU;AAAA,EAKV,WAAW;AAAA,EAKX,UAAU;AAAA,EAKV,eAAe;AAAA,EAKf,cAAc;AAAA,EAKd,cAAc;AACf;AAUO,IAAK,QAAL,kBAAK,WAAL;AAIN,oBAAQ;AAKR,wBAAY;AAKZ,qBAAS;AAdE;AAAA;;;ACtSZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;ACWO,IAAM,0BAAN,MAA8B;AAAA,EACpC,AAAO,YAAmB,OAAqC,CAAC,GAAG;AAAzC;AAAA,EAA0C;AAAA,EAOpE,AAAO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,+BAA+B,MAAM,KAAK;AAC5D,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,+BAA+B,MAAM,KAAK;AAC5D,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAc,+BAA+B,MAAM,WAAW;AACxE,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,WAAW,YAAY,MAAM;AACnC,SAAK,KAAK,UAAU,iBAAiB,MAAM,SAAS;AACpD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAiC;AAChD,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAA8B;AACpC,+CAA2C,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAE3E,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AACD;AA5Da;;;ADPN,IAAM,oBAAoB,GAAE,OACjC,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AAEnC,IAAM,iBAAiB,GAC5B,OAAO;AAAA,EACP,IAAI,GAAE;AAAA,EACN,MAAM,GAAE;AAAA,EACR,UAAU,GAAE;AACb,CAAC,EACA,QAAQ,OAAO,qBAAqB,mBAAmB;AAElD,IAAM,oBAAoB,GAAE;AAE5B,IAAM,uBAAuB,GAAE,OACpC,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,qBAAqB,mBAAmB;AAEnC,IAAM,uBAAuB,GAAE,WAAW,WAAW;AAErD,IAAM,uBAAuB,GAAE,OAAO,sBAAsB,GAAG,EAAE,qBAAqB,mBAAmB;AACzG,IAAM,kBAAkB,GAAE,OAAO,IACtC,mBAAmB,CAAC,EACpB,gBAAgB,EAAE,EAClB,qBAAqB,mBAAmB;AAEnC,IAAM,iCAAiC,GAAE,OAC9C,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AACnC,IAAM,kBAAkB,GAC7B,MACA,GAAE,OAAO;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa,+BAA+B;AAAA,EAC5C,OAAO,eAAe;AAAA,EACtB,SAAS,GAAE,QAAQ;AACpB,CAAC,GACD,GAAE,SAAS,uBAAuB,CACnC,EACC,qBAAqB,mBAAmB;AAEnC,IAAM,mBAAmB,gBAAgB,MAC9C,yBAAyB,CAAC,EAC1B,qBAAqB,mBAAmB;AACnC,IAAM,yBAAyB,GAAE,OAAO,IAC7C,mBAAmB,CAAC,EACpB,gBAAgB,EAAE,EAClB,qBAAqB,mBAAmB;AAEnC,8CAA8C,SAAoC,UAAmB;AAC3G,oBAAkB,MAAM,QAAQ;AAChC,mBAAiB,MAAM,OAAO;AAC/B;AAHgB;AAKT,IAAM,mBAAmB,GAAE;AAE3B,oDAAoD,OAAgB,OAAgB;AAC1F,iCAA+B,MAAM,KAAK;AAC1C,iCAA+B,MAAM,KAAK;AAC3C;AAHgB;AAKT,IAAM,eAAe,GAAE,OAC5B,IAAI;AAAA,EACJ,kBAAkB,CAAC,SAAS,UAAU,UAAU;AACjD,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,0CACN,OACA,OACA,OACA,UACA,KACC;AACD,MAAI,OAAO,UAAU;AACpB,UAAM,IAAI,WAAW,0CAA0C;AAAA,EAChE;AAEA,MAAI,CAAC,SAAS,CAAC,OAAO;AACrB,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACjE;AAEA,MAAI,UAAU,YAAY,MAAM;AAC/B,QAAI,CAAC,KAAK;AACT,YAAM,IAAI,WAAW,8BAA8B;AAAA,IACpD;AAAA,EACD,WAAW,KAAK;AACf,UAAM,IAAI,WAAW,oCAAoC;AAAA,EAC1D;AACD;AAtBgB;;;AE5EhB;AAAA;AAAA;;;ACaO,IAAe,mBAAf,MAGP;AAAA,EAQC,AAAO,YAAY,MAAyB;AAJ5C,wBAAgB;AAKf,SAAK,OAAO;AAAA,EACb;AACD;AAdsB;;;ACbtB;;;ACAA;AAAA;AAAA;AAsBO,IAAM,gBAAN,cAA4B,iBAAqC;AAAA,EACvE,AAAO,YAAY,MAAoC;AACtD,UAAM,EAAE,MAAM,cAAc,QAAQ,GAAG,KAAK,CAAC;AAAA,EAC9C;AAAA,EAOA,AAAO,SAAS,OAAoB;AACnC,SAAK,KAAK,QAAQ,qBAAqB,MAAM,KAAK;AAClD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,OAAO,KAAa;AAC1B,IAAC,KAAK,KAAmC,MAAM,aAAa,MAAM,GAAG;AACrE,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,YAAY,UAAkB;AACpC,IAAC,KAAK,KAAwC,YAAY,kBAAkB,MAAM,QAAQ;AAC1F,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAiC;AAChD,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,YAAY,WAAW,MAAM;AACnC,SAAK,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,qBAAqB,MAAM,KAAK;AAClD,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAA6B;AACnC,qCACC,KAAK,KAAK,OACV,KAAK,KAAK,OACV,KAAK,KAAK,OACT,KAAK,KAAwC,WAC7C,KAAK,KAAmC,GAC1C;AAEA,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AACD;AA9Ea;;;ACtBb;AAiBO,IAAM,oBAAN,cAAgC,iBAAyC;AAAA,EAM/E,AAAO,YAAY,MAAwC;AAC1D,UAAM,EAAE,YAAY,aAAa,QAAQ,CAAC;AAC1C,UAAM,EAAE,MAAM,eAAc,YAAY,GAAG,SAAS,CAAC;AAJtD,wBAAgB;AAKf,SAAK,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI,wBAAwB,CAAC,CAAC,KAAK,CAAC;AAAA,EACxE;AAAA,EAOA,AAAO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAc,qBAAqB,MAAM,WAAW;AAC9D,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,gBAAgB,MAAM,SAAS;AACtD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,gBAAgB,MAAM,SAAS;AACtD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,YAAY,UAAkB;AACpC,SAAK,KAAK,YAAY,kBAAkB,MAAM,QAAQ;AACtD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,YAAY,WAAW,MAAM;AACnC,SAAK,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA,EAQA,AAAO,cAAc,SAAqE;AACzF,cAAU,eAAe,OAAO;AAChC,2BAAuB,MAAM,KAAK,QAAQ,SAAS,QAAQ,MAAM;AACjE,SAAK,QAAQ,KACZ,GAAG,QAAQ,IAAI,CAAC,WACf,kBAAkB,0BACf,SACA,IAAI,wBAAwB,gBAAgB,MAA2B,MAAM,CAAC,CAClF,CACD;AACA,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,cAAc,SAAqE;AACzF,cAAU,eAAe,OAAO;AAChC,2BAAuB,MAAM,QAAQ,MAAM;AAC3C,SAAK,QAAQ,OACZ,GACA,KAAK,QAAQ,QACb,GAAG,QAAQ,IAAI,CAAC,WACf,kBAAkB,0BACf,SACA,IAAI,wBAAwB,gBAAgB,MAA2B,MAAM,CAAC,CAClF,CACD;AACA,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAAiC;AACvC,yCAAqC,KAAK,SAAS,KAAK,KAAK,SAAS;AAEtE,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,SAAS,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAAA,IAC5C;AAAA,EACD;AACD;AA7Ga;;;ACjBb;AACA;;;ACDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIO,IAAM,0BAA0B,GAAE,WAAW,cAAc;AAC3D,IAAM,qBAAqB,GAAE,OAAO,IACzC,mBAAmB,CAAC,EACpB,gBAAgB,GAAI,EACpB,qBAAqB,mBAAmB;AACnC,IAAM,qBAAqB,GAAE,OAAO,IACzC,mBAAmB,CAAC,EACpB,gBAAgB,GAAI,EACpB,qBAAqB,mBAAmB;AACnC,IAAM,oBAAoB,GAAE;AAC5B,IAAM,iBAAiB,GAAE,OAAO,sBAAsB,GAAI,EAAE,qBAAqB,mBAAmB;AACpG,IAAM,wBAAuB,GAAE,OAAO,sBAAsB,GAAG,EAAE,qBAAqB,mBAAmB;AACzG,IAAM,iBAAiB,GAAE,OAC9B,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,qBAAqB,mBAAmB;AAEnC,oCAAoC,UAAmB,OAAwB,OAAgB;AACrG,oBAAkB,MAAM,QAAQ;AAChC,0BAAwB,MAAM,KAAK;AACnC,iBAAe,MAAM,KAAK;AAC3B;AAJgB;;;ACXT,yBAAyB,gBAAmE;AAClG,SAAO,mBAAmB,QAAQ,OAAO,mBAAmB,YAAY,YAAY;AACrF;AAFgB;;;AFKT,IAAM,mBAAN,cAA+B,iBAAwC;AAAA,EAC7E,AAAO,YAAY,MAAmE;AACrF,UAAM,EAAE,MAAM,eAAc,WAAW,GAAG,KAAK,CAAC;AAAA,EACjD;AAAA,EAOA,AAAO,YAAY,UAAkB;AACpC,SAAK,KAAK,YAAY,kBAAkB,MAAM,QAAQ;AACtD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAuB;AACtC,SAAK,KAAK,QAAQ,wBAAwB,MAAM,KAAK;AACrD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,mBAAmB,MAAM,SAAS;AACzD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,mBAAmB,MAAM,SAAS;AACzD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAc,sBAAqB,MAAM,WAAW;AAC9D,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,YAAY,WAAW,MAAM;AACnC,SAAK,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAAgC;AACtC,+BAA2B,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAEhF,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AAAA,EAEA,AAAO,OAAO,OAA8E;AAC3F,QAAI,gBAAgB,KAAK,GAAG;AAC3B,aAAO,QAAQ,MAAM,OAAO,GAAG,KAAK,IAAI;AAAA,IACzC;AAEA,WAAO,QAAQ,OAAO,KAAK,IAAI;AAAA,EAChC;AACD;AApGa;;;AHYN,gCACN,MACmB;AACnB,MAAI,gBAAgB,kBAAkB;AACrC,WAAO;AAAA,EACR;AAEA,UAAQ,KAAK;AAAA,SACP,eAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA,SAC5B,eAAc;AAClB,aAAO,IAAI,cAAc,IAAI;AAAA,SACzB,eAAc;AAClB,aAAO,IAAI,kBAAkB,IAAI;AAAA,SAC7B,eAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA;AAIhC,YAAM,IAAI,MAAM,6CAA6C,KAAK,MAAM;AAAA;AAE3E;AArBgB;;;AFHT,IAAM,mBAAN,cAA8D,iBAEnE;AAAA,EAMD,AAAO,YAAY,EAAE,eAAe,SAAqE,CAAC,GAAG;AAC5G,UAAM,EAAE,MAAM,eAAc,WAAW,GAAG,KAAK,CAAC;AAHjD,wBAAgB;AAIf,SAAK,aAAc,YAAY,IAAI,CAAC,MAAM,uBAAuB,CAAC,CAAC,KAAK,CAAC;AAAA,EAC1E;AAAA,EAOA,AAAO,iBAAiB,YAA4B;AACnD,SAAK,WAAW,KAAK,GAAG,eAAe,UAAU,CAAC;AAClD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,iBAAiB,YAA4B;AACnD,SAAK,WAAW,OAAO,GAAG,KAAK,WAAW,QAAQ,GAAG,eAAe,UAAU,CAAC;AAC/E,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAAyD;AAE/D,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,YAAY,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,OAAO,CAAC;AAAA,IAClE;AAAA,EACD;AACD;AAxCa;;;AQzBb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKO,IAAM,iBAAiB,GAAE,OAC9B,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,qBAAqB,mBAAmB;AACnC,IAAM,sBAAsB,GACjC,SAAS,gBAAgB,EACzB,MAAM,yBAAyB,CAAC,EAChC,qBAAqB,mBAAmB;AAEnC,qCACN,UACA,OACA,YACC;AACD,oBAAkB,MAAM,QAAQ;AAChC,iBAAe,MAAM,KAAK;AAC1B,sBAAoB,MAAM,UAAU;AACrC;AARgB;;;ACFT,IAAM,eAAN,MAAqF;AAAA,EAI3F,AAAO,YAAY,EAAE,eAAe,SAA2D,CAAC,GAAG;AAHnG,wBAAgB;AAChB,wBAAgB,cAAiE,CAAC;AAGjF,SAAK,OAAO,EAAE,GAAG,KAAK;AACtB,SAAK,aAAc,YAAY,IAAI,CAAC,MAAM,uBAAuB,CAAC,CAAC,KAClE,CAAC;AAAA,EACH;AAAA,EAOA,AAAO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,YAAY,UAAkB;AACpC,SAAK,KAAK,YAAY,kBAAkB,MAAM,QAAQ;AACtD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,iBACH,YAGF;AACD,SAAK,WAAW,KACf,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAClC,qBAAqB,mBAClB,YACA,IAAI,iBAAiD,SAAS,CAClE,CACD;AACA,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,iBAAiB,YAA2E;AAClG,SAAK,WAAW,OAAO,GAAG,KAAK,WAAW,QAAQ,GAAG,eAAe,UAAU,CAAC;AAC/E,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAAkD;AACxD,gCAA2B,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,KAAK,UAAU;AAEhF,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,YAAY,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,OAAO,CAAC;AAAA,IAClE;AAAA,EACD;AACD;AApEa;;;ACZb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAMA,IAAM,gBAAgB,GAAE,OACtB,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,MAAM,6DAA6D,EACnE,qBAAqB,mBAAmB;AAEnC,sBAAsB,MAAuC;AACnE,gBAAc,MAAM,IAAI;AACzB;AAFgB;AAIhB,IAAM,wBAAuB,GAAE,OAC7B,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AAC1C,IAAM,kBAAkB,GAAE,WAAW,MAAM;AAEpC,6BAA6B,aAAqD;AACxF,wBAAqB,MAAM,WAAW;AACvC;AAFgB;AAIhB,IAAM,0BAA0B,GAAE,QAAQ,MAAM,sBAAsB,EAAE,EAAE,qBAAqB,mBAAmB;AAC3G,wBAAwB,QAAiB;AAC/C,SAAO,gBAAgB,MAAM,MAAM;AACpC;AAFgB;AAIT,kCAAkC,SAAuE;AAC/G,0BAAwB,MAAM,OAAO;AACtC;AAFgB;AAIT,qCACN,MACA,aACA,SACC;AAED,eAAa,IAAI;AAGjB,sBAAoB,WAAW;AAG/B,2BAAyB,OAAO;AACjC;AAbgB;AAehB,IAAM,mBAAmB,GAAE;AAEpB,mCAAmC,OAA0C;AACnF,mBAAiB,MAAM,KAAK;AAC7B;AAFgB;AAIT,0BAA0B,UAAgD;AAChF,mBAAiB,MAAM,QAAQ;AAChC;AAFgB;AAIhB,IAAM,yBAAyB,GAAE,OAAO,gBAAgB,EAAE,EAAE,qBAAqB,mBAAmB;AAE7F,+BAA+B,cAAsB,SAAqD;AAChH,yBAAuB,MAAO,UAAS,UAAU,KAAK,YAAY;AACnE;AAFgB;AAIT,+BAEL,OAAgB,oBAAqD;AACtE,KAAE,SAAS,kBAAkB,EAAE,MAAM,KAAK;AAC3C;AAJgB;AAMT,IAAM,2BAA2B,GACtC,OAAwB,OAAO,YAAY,OAAO,OAAO,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAE,OAAO,OAAO,CAAC,CAAC,CAAC,EAC7G,OAAO,QAAQ,qBAAqB,mBAAmB;AAElD,iCAAiC,OAAkD;AACzF,2BAAyB,MAAM,KAAK;AACrC;AAFgB;AAIhB,IAAM,wBAAwB,GAAE,QAAQ;AAEjC,8BAA8B,OAA6D;AACjG,wBAAsB,MAAM,KAAK;AAClC;AAFgB;AAIhB,IAAM,4BAA4B,GAAE,MACnC,GAAE,OAAO,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC,GAC9C,GAAE,OAAO,QAAQ,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC,GACtD,GAAE,OAAO,MAAM,OAAO,CACvB,EAAE;AAEK,0CAA0C,aAAsB;AACtE,SAAO,0BAA0B,MAAM,WAAW;AACnD;AAFgB;;;ACvFhB;;;ACNA;AAAA;AAAA;AAKA;;;ACFO,IAAM,2BAAN,MAA+B;AAAA,EAA/B;AACN,wBAAgB;AAChB,wBAAgB;AAChB,wBAAgB;AAChB,wBAAgB;AAAA;AAAA,EAOhB,AAAO,QAAQ,MAAoB;AAElC,iBAAa,IAAI;AAEjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,eAAe,aAAqB;AAE1C,wBAAoB,WAAW;AAE/B,YAAQ,IAAI,MAAM,eAAe,WAAW;AAE5C,WAAO;AAAA,EACR;AAAA,EAQA,AAAO,oBAAoB,QAAsB,eAA8B;AAC9E,QAAI,CAAC,KAAK,oBAAoB;AAC7B,cAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAAA,IAC3C;AAEA,UAAM,eAAe,eAAe,MAAM;AAE1C,QAAI,kBAAkB,MAAM;AAC3B,WAAK,mBAAoB,gBAAgB;AACzC,aAAO;AAAA,IACR;AAEA,iBAAa,aAAa;AAE1B,SAAK,mBAAoB,gBAAgB;AACzC,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,qBAAqB,gBAAwC;AACnE,QAAI,mBAAmB,MAAM;AAC5B,cAAQ,IAAI,MAAM,sBAAsB,IAAI;AAC5C,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAE1C,WAAO,QAAQ,cAAc,EAAE,QAAQ,CAAC,SACvC,KAAK,oBAAoB,GAAI,IAAsC,CACpE;AACA,WAAO;AAAA,EACR;AAAA,EAQA,AAAO,2BAA2B,QAAsB,sBAAqC;AAC5F,QAAI,CAAC,KAAK,2BAA2B;AACpC,cAAQ,IAAI,MAAM,6BAA6B,CAAC,CAAC;AAAA,IAClD;AAEA,UAAM,eAAe,eAAe,MAAM;AAE1C,QAAI,yBAAyB,MAAM;AAClC,WAAK,0BAA2B,gBAAgB;AAChD,aAAO;AAAA,IACR;AAEA,wBAAoB,oBAAoB;AAExC,SAAK,0BAA2B,gBAAgB;AAChD,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,4BAA4B,uBAA+C;AACjF,QAAI,0BAA0B,MAAM;AACnC,cAAQ,IAAI,MAAM,6BAA6B,IAAI;AACnD,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,6BAA6B,CAAC,CAAC;AACjD,WAAO,QAAQ,qBAAqB,EAAE,QAAQ,CAAC,SAC9C,KAAK,2BAA2B,GAAI,IAAsC,CAC3E;AACA,WAAO;AAAA,EACR;AACD;AAtHa;;;ACHb;;;ACIO,IAAe,+BAAf,cAAoD,yBAAyB;AAAA,EAA7E;AAAA;AAGN,wBAAgB,YAAoB;AAAA;AAAA,EAOpC,AAAO,YAAY,UAAmB;AAErC,qBAAiB,QAAQ;AAEzB,YAAQ,IAAI,MAAM,YAAY,QAAQ;AAEtC,WAAO;AAAA,EACR;AAAA,EAIA,AAAU,yBAAyB;AAClC,gCAA2B,KAAK,MAAM,KAAK,aAAa,CAAC,CAAC;AAG1D,4BAAwB,KAAK,kBAAkB;AAC/C,4BAAwB,KAAK,yBAAyB;AAGtD,qBAAiB,KAAK,QAAQ;AAAA,EAC/B;AACD;AA/BsB;;;ADDf,IAAM,+BAAN,cAA2C,6BAA6B;AAAA,EAAxE;AAAA;AACN,wBAAyB,QAAO,6BAA6B;AAAA;AAAA,EAE7D,AAAO,SAAgD;AACtD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AARa;;;AEHb;AAGO,IAAM,4BAAN,cAAwC,6BAA6B;AAAA,EAArE;AAAA;AACN,wBAAgB,QAAO,8BAA6B;AAAA;AAAA,EAEpD,AAAO,SAA6C;AACnD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AARa;;;ACHb;AACA;;;ACDA;AACA;AAGA,IAAM,sBAAsB;AAAA,EAC3B,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AACb;AAIA,IAAM,wBAAwB,GAAE,MAAM,GAAE,MAAM,GAAG,oBAAoB,IAAI,CAAC,SAAS,GAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;AAE7F,IAAM,4CAAN,MAAgD;AAAA,EAAhD;AACN,wBAAgB;AAAA;AAAA,EAOhB,AAAO,mBAAmB,cAA6D;AACtF,QAAI,KAAK,kBAAkB,QAAW;AACrC,cAAQ,IAAI,MAAM,iBAAiB,CAAC,CAAC;AAAA,IACtC;AAEA,SAAK,cAAe,KAAK,GAAG,sBAAsB,MAAM,YAAY,CAAC;AAErE,WAAO;AAAA,EACR;AACD;AAjBa;;;ADbN,IAAM,4BAAN,cAAwC,6BAA6B;AAAA,EAArE;AAAA;AACN,wBAAyB,QAAO,8BAA6B;AAAA;AAAA,EAE7D,AAAO,SAA6C;AACnD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AARa;AAAA,4BAAN;AAAA,EADP,AAAC,IAAI,yCAAyC;AAAA,GACjC;;;AENb;AACA;AACA;;;ACFO,IAAe,kDAAf,MAA+D;AAAA,EAA/D;AACN,wBAAgB;AAChB,wBAAgB;AAAA;AAejB;AAjBsB;;;ACAtB;AACA;AAGA,IAAM,kBAAkB,GAAE,OAAO,yBAAyB,CAAC,EAAE,sBAAsB,GAAG;AACtF,IAAM,kBAAkB,GAAE,OAAO,YAAY,SAAS,EAAE,SAAS,QAAQ;AACzE,IAAM,mBAAmB,GAAE,OAAO;AAAA,EACjC,MAAM;AAAA,EACN,oBAAoB;AAAA,EACpB,OAAO,GAAE,MAAM,iBAAiB,eAAe;AAChD,CAAC,EAAE;AACH,IAAM,oBAAmB,GAAE;AAEpB,IAAM,0DAAN,MAAyF;AAAA,EAAzF;AACN,wBAAgB;AAChB,wBAAgB;AAGhB,wBAAgB;AAAA;AAAA,EAOhB,AAAO,cAAc,SAAuD;AAC3E,QAAI,QAAQ,SAAS,KAAK,KAAK,cAAc;AAC5C,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,qBAAiB,MAAM,OAAO;AAE9B,QAAI,KAAK,YAAY,QAAW;AAC/B,cAAQ,IAAI,MAAM,WAAW,CAAC,CAAC;AAAA,IAChC;AAEA,0BAAsB,QAAQ,QAAQ,KAAK,OAAO;AAElD,eAAW,EAAE,MAAM,oBAAoB,WAAW,SAAS;AAE1D,UAAI,KAAK,SAAS,8BAA6B,QAAQ;AACtD,wBAAgB,MAAM,KAAK;AAAA,MAC5B,OAAO;AACN,wBAAgB,MAAM,KAAK;AAAA,MAC5B;AAEA,WAAK,QAAS,KAAK,EAAE,MAAM,oBAAoB,MAAM,CAAC;AAAA,IACvD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,cAAoE,SAAsB;AAChG,QAAI,QAAQ,SAAS,KAAK,KAAK,cAAc;AAC5C,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,qBAAiB,MAAM,OAAO;AAE9B,YAAQ,IAAI,MAAM,WAAW,CAAC,CAAC;AAC/B,SAAK,WAAW,GAAG,OAAO;AAE1B,WAAO;AAAA,EACR;AAAA,EAMA,AAAO,gBAAgB,cAA6B;AAEnD,sBAAiB,MAAM,YAAY;AAEnC,QAAI,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAC3E,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,YAAQ,IAAI,MAAM,gBAAgB,YAAY;AAE9C,WAAO;AAAA,EACR;AACD;AApEa;;;AFNb,IAAM,kBAAkB,GAAE,OAAO;AAG1B,IAAM,4BAAN,cACE,6BAET;AAAA,EAHO;AAAA;AAIN,wBAAgB,QAAO,8BAA6B;AAAA;AAAA,EAEpD,AAAO,YAAY,KAAmB;AACrC,oBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,YAAY,KAAmB;AACrC,oBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAA6C;AACnD,SAAK,uBAAuB;AAE5B,QAAI,KAAK,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AA/Ba;AAAA,4BAAN;AAAA,EADP,AAAC,KAAI,iDAAiD,uDAAuD;AAAA,GAChG;;;AGVb;AAGO,IAAM,gCAAN,cAA4C,6BAA6B;AAAA,EAAzE;AAAA;AACN,wBAAgB,QAAO,8BAA6B;AAAA;AAAA,EAEpD,AAAO,SAAiD;AACvD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AARa;;;ACHb;AACA;AACA;AAKA,IAAM,mBAAkB,GAAE;AAGnB,IAAM,2BAAN,cACE,6BAET;AAAA,EAHO;AAAA;AAIN,wBAAgB,QAAO,8BAA6B;AAAA;AAAA,EAEpD,AAAO,YAAY,KAAmB;AACrC,qBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,YAAY,KAAmB;AACrC,qBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAA4C;AAClD,SAAK,uBAAuB;AAE5B,QAAI,KAAK,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AA/Ba;AAAA,2BAAN;AAAA,EADP,AAAC,KAAI,iDAAiD,uDAAuD;AAAA,GAChG;;;ACVb;AAGO,IAAM,yBAAN,cAAqC,6BAA6B;AAAA,EAAlE;AAAA;AACN,wBAAyB,QAAO,8BAA6B;AAAA;AAAA,EAE7D,AAAO,SAA0C;AAChD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AARa;;;ACHb;AACA;AACA;AAIA,IAAM,sBAAqB,IAAE,OAAO,mBAAmB,CAAC,EAAE,gBAAgB,GAAI;AAC9E,IAAM,sBAAqB,IAAE,OAAO,mBAAmB,CAAC,EAAE,gBAAgB,GAAI;AAGvE,IAAM,2BAAN,cAAuC,6BAA6B;AAAA,EAApE;AAAA;AACN,wBAAgB,QAAO,8BAA6B;AACpD,wBAAgB;AAChB,wBAAgB;AAAA;AAAA,EAOhB,AAAO,aAAa,KAAmB;AACtC,wBAAmB,MAAM,GAAG;AAE5B,YAAQ,IAAI,MAAM,cAAc,GAAG;AAEnC,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,aAAa,KAAmB;AACtC,wBAAmB,MAAM,GAAG;AAE5B,YAAQ,IAAI,MAAM,cAAc,GAAG;AAEnC,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAA4C;AAClD,SAAK,uBAAuB;AAE5B,QAAI,KAAK,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AAxCa;AAAA,2BAAN;AAAA,EADP,AAAC,KAAI,uDAAuD;AAAA,GAC/C;;;ACVb;AAGO,IAAM,yBAAN,cAAqC,6BAA6B;AAAA,EAAlE;AAAA;AACN,wBAAgB,QAAO,+BAA6B;AAAA;AAAA,EAEpD,AAAO,SAA0C;AAChD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AARa;;;ACUN,IAAM,4BAAN,MAAsE;AAAA,EAAtE;AACN,wBAAgB;AAAA;AAAA,EAOhB,AAAO,iBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,yBAAyB;AAAA,EACpE;AAAA,EAOA,AAAO,cAAc,OAA+F;AACnH,WAAO,KAAK,uBAAuB,OAAO,sBAAsB;AAAA,EACjE;AAAA,EAOA,AAAO,iBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,yBAAyB;AAAA,EACpE;AAAA,EAOA,AAAO,cAAc,OAA+F;AACnH,WAAO,KAAK,uBAAuB,OAAO,sBAAsB;AAAA,EACjE;AAAA,EAOA,AAAO,oBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,4BAA4B;AAAA,EACvE;AAAA,EAOA,AAAO,qBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,6BAA6B;AAAA,EACxE;AAAA,EAOA,AAAO,gBACN,OAUC;AACD,WAAO,KAAK,uBAAuB,OAAO,wBAAwB;AAAA,EACnE;AAAA,EAOA,AAAO,iBACN,OAUC;AACD,WAAO,KAAK,uBAAuB,OAAO,yBAAyB;AAAA,EACpE;AAAA,EAOA,AAAO,gBACN,OAUC;AACD,WAAO,KAAK,uBAAuB,OAAO,wBAAwB;AAAA,EACnE;AAAA,EAEA,AAAQ,uBACP,OAKA,UACyG;AACzG,UAAM,EAAE,YAAY;AAGpB,6BAAyB,OAAO;AAGhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,SAAS,CAAC,IAAI;AAErE,0BAAsB,QAAQ,QAAQ;AAGtC,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AACD;AApJa;;;AfKN,IAAM,qCAAN,MAAmF;AAAA,EAAnF;AAIN,wBAAgB;AAKhB,wBAAgB;AAKhB,wBAAgB,WAA2C,CAAC;AAAA;AAAA,EAO5D,AAAO,cACN,OAGC;AACD,UAAM,EAAE,YAAY;AAGpB,6BAAyB,OAAO;AAIhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,8BAA8B,CAAC,IAAI;AAG1F,0BAAsB,QAAQ,6BAA6B;AAG3D,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AAAA,EAEA,AAAO,SAAqD;AAC3D,gCAA2B,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAEpE,WAAO;AAAA,MACN,MAAM,+BAA6B;AAAA,MACnC,MAAM,KAAK;AAAA,MACX,oBAAoB,KAAK;AAAA,MACzB,aAAa,KAAK;AAAA,MAClB,2BAA2B,KAAK;AAAA,MAChC,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AACD;AAxDa;AAAA,qCAAN;AAAA,EADP,AAAC,KAAI,wBAAwB;AAAA,GAChB;AAkEN,IAAM,gCAAN,MAA8E;AAAA,EAA9E;AAIN,wBAAgB;AAKhB,wBAAgB;AAKhB,wBAAgB,WAA0C,CAAC;AAAA;AAAA,EAE3D,AAAO,SAAgD;AACtD,gCAA2B,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAEpE,WAAO;AAAA,MACN,MAAM,+BAA6B;AAAA,MACnC,MAAM,KAAK;AAAA,MACX,oBAAoB,KAAK;AAAA,MACzB,aAAa,KAAK;AAAA,MAClB,2BAA2B,KAAK;AAAA,MAChC,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AACD;AA5Ba;AAAA,gCAAN;AAAA,EADP,AAAC,KAAI,0BAA0B,yBAAyB;AAAA,GAC3C;;;AD/DN,IAAM,sBAAN,MAA0B;AAAA,EAA1B;AAIN,wBAAgB;AAKhB,wBAAgB;AAKhB,wBAAgB;AAKhB,wBAAgB;AAKhB,wBAAgB,WAA4C,CAAC;AAQ7D,wBAAgB;AAKhB,wBAAgB;AAMhB,wBAAgB;AAAA;AAAA,EAOhB,AAAO,SAAiD;AACvD,gCAA2B,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAEpE,4BAAwB,KAAK,kBAAkB;AAC/C,4BAAwB,KAAK,yBAAyB;AAEtD,WAAO;AAAA,MACN,GAAG;AAAA,MACH,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AAAA,EAYA,AAAO,qBAAqB,OAAgB;AAE3C,8BAA0B,KAAK;AAE/B,YAAQ,IAAI,MAAM,sBAAsB,KAAK;AAE7C,WAAO;AAAA,EACR;AAAA,EAWA,AAAO,4BAA4B,aAA+D;AAEjG,UAAM,kBAAkB,iCAAiC,WAAW;AAEpE,YAAQ,IAAI,MAAM,8BAA8B,eAAe;AAE/D,WAAO;AAAA,EACR;AAAA,EAUA,AAAO,gBAAgB,SAAqC;AAE3D,yBAAqB,OAAO;AAE5B,YAAQ,IAAI,MAAM,iBAAiB,OAAO;AAE1C,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,mBACN,OAGqC;AACrC,UAAM,EAAE,YAAY;AAGpB,6BAAyB,OAAO;AAGhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,mCAAmC,CAAC,IAAI;AAE/F,0BAAsB,QAAQ,kCAAkC;AAGhE,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,cACN,OAGqC;AACrC,UAAM,EAAE,YAAY;AAGpB,6BAAyB,OAAO;AAGhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,8BAA8B,CAAC,IAAI;AAE1F,0BAAsB,QAAQ,6BAA6B;AAG3D,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AACD;AAvKa;AAAA,sBAAN;AAAA,EADP,AAAC,KAAI,2BAA2B,wBAAwB;AAAA,GAC3C;;;AiBrBb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA,IAAM,iBAAgB,IAAE,OACtB,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,MAAM,uDAAuD,EAC7D,qBAAqB,mBAAmB;AAC1C,IAAM,gBAAgB,IACpB,MAAM,IAAE,QAAQ,uBAAuB,IAAI,GAAG,IAAE,QAAQ,uBAAuB,OAAO,CAAC,EACvF,qBAAqB,mBAAmB;AAC1C,IAAM,oBAAmB,IAAE;AAEpB,oCAAmC,OAA0C;AACnF,oBAAiB,MAAM,KAAK;AAC7B;AAFgB;AAIT,uBAAsB,MAAuC;AACnE,iBAAc,MAAM,IAAI;AACzB;AAFgB;AAIT,sBAAsB,MAAuD;AACnF,gBAAc,MAAM,IAAI;AACzB;AAFgB;AAIT,qCAAoC,MAAc,MAAc;AAEtE,gBAAa,IAAI;AAGjB,eAAa,IAAI;AAClB;AANgB;AAQhB,IAAM,yBAAwB,IAAE,QAAQ;AAEjC,+BAA8B,OAA6D;AACjG,yBAAsB,MAAM,KAAK;AAClC;AAFgB;AAIhB,IAAM,6BAA4B,IAAE,MACnC,IAAE,OAAO,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC,GAC9C,IAAE,OAAO,QAAQ,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC,GACtD,IAAE,OAAO,MAAM,OAAO,CACvB,EAAE;AAEK,2CAA0C,aAAsB;AACtE,SAAO,2BAA0B,MAAM,WAAW;AACnD;AAFgB;;;AC9BT,IAAM,4BAAN,MAAgC;AAAA,EAAhC;AAIN,wBAAgB;AAKhB,wBAAgB;AAKhB,wBAAgB;AAQhB,wBAAgB;AAKhB,wBAAgB;AAMhB,wBAAgB;AAAA;AAAA,EAOhB,AAAO,QAAQ,MAAc;AAE5B,kBAAa,IAAI;AAEjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,QAAQ,MAA8B;AAE5C,iBAAa,IAAI;AAEjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA,EAYA,AAAO,qBAAqB,OAAgB;AAE3C,+BAA0B,KAAK;AAE/B,YAAQ,IAAI,MAAM,sBAAsB,KAAK;AAE7C,WAAO;AAAA,EACR;AAAA,EAWA,AAAO,4BAA4B,aAA+D;AAEjG,UAAM,kBAAkB,kCAAiC,WAAW;AAEpE,YAAQ,IAAI,MAAM,8BAA8B,eAAe;AAE/D,WAAO;AAAA,EACR;AAAA,EAUA,AAAO,gBAAgB,SAAqC;AAE3D,0BAAqB,OAAO;AAE5B,YAAQ,IAAI,MAAM,iBAAiB,OAAO;AAE1C,WAAO;AAAA,EACR;AAAA,EAQA,AAAO,oBAAoB,QAAsB,eAA8B;AAC9E,QAAI,CAAC,KAAK,oBAAoB;AAC7B,cAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAAA,IAC3C;AAEA,UAAM,eAAe,eAAe,MAAM;AAE1C,QAAI,kBAAkB,MAAM;AAC3B,WAAK,mBAAoB,gBAAgB;AACzC,aAAO;AAAA,IACR;AAEA,kBAAa,aAAa;AAE1B,SAAK,mBAAoB,gBAAgB;AACzC,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,qBAAqB,gBAAwC;AACnE,QAAI,mBAAmB,MAAM;AAC5B,cAAQ,IAAI,MAAM,sBAAsB,IAAI;AAC5C,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAE1C,WAAO,QAAQ,cAAc,EAAE,QAAQ,CAAC,SACvC,KAAK,oBAAoB,GAAI,IAAsC,CACpE;AACA,WAAO;AAAA,EACR;AAAA,EAOA,AAAO,SAAiD;AACvD,gCAA2B,KAAK,MAAM,KAAK,IAAI;AAE/C,4BAAwB,KAAK,kBAAkB;AAE/C,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AA5Ka;;;ACNN,qBAAqB,gBAA+D;AAC1F,SAAO,mBAAmB,QAAQ,OAAO,mBAAmB,YAAY,YAAY;AACrF;AAFgB;;;ACTT,qBAAqB,MAAgB;AAC3C,SACE,MAAK,OAAO,UAAU,KACtB,MAAK,aAAa,UAAU,KAC5B,MAAK,QAAQ,OAAO,CAAC,MAAM,SAAS,OAAO,KAAK,KAAK,SAAS,KAAK,MAAM,QAAQ,CAAC,KAAK,KACvF,MAAK,QAAQ,KAAK,UAAU,KAC5B,MAAK,QAAQ,KAAK,UAAU;AAE/B;AARgB;", "names": []}