import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
import type { Snowflake } from '../../../../../globals';
/**
 * @deprecated API and gateway v8 are deprecated and the types will not receive further updates, please update to v10.
 */
export declare type APIApplicationCommandMentionableOption = APIApplicationCommandOptionBase<ApplicationCommandOptionType.Mentionable>;
/**
 * @deprecated API and gateway v8 are deprecated and the types will not receive further updates, please update to v10.
 */
export declare type APIApplicationCommandInteractionDataMentionableOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.Mentionable, Snowflake>;
//# sourceMappingURL=mentionable.d.ts.map